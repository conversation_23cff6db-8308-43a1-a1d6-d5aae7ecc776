import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';
import upivotalFavicon from '@/assets/images/uPivotalFavicon.png';
import pivotlFavicon from '@/apps/pivoTL/assets/icons/favicon.ico';

export const DynamicHead = () => {
  const { pathname } = useLocation();
  const isPivoTL = pathname.startsWith('/pivotl');

  return (
    <Helmet>
      {/* Title & Favicon - different for each app */}
      <title>{isPivoTL ? 'PivoTL' : 'uPivotal'}</title>
      <link
        rel="icon"
        type={isPivoTL ? 'image/x-icon' : 'image/png'}
        href={isPivoTL ? pivotlFavicon : upivotalFavicon}
      />
      <meta property="og:title" content={isPivoTL ? 'PivoTL' : 'uPivotal'} />
      <meta
        property="og:description"
        content={
          isPivoTL
            ? 'The AI Agent Marketplace and Transformation Layer'
            : "You've Received an Update! See What It's About."
        }
      />
      <meta property="og:url" content={window.location.href} />
    </Helmet>
  );
};
