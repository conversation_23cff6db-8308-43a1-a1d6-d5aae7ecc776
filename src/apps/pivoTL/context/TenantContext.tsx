import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';

import { TenantInfo } from '../types/user';

export interface Tenant {
  id: string;
  agentSuiteKey: string;
  name: string;
  logo?: string;
  tenant?: TenantInfo; // Store full tenant data for access to plan, status, etc.
}

interface TenantContextType {
  tenants: Tenant[];
  selectedTenant: Tenant | null;
  setTenants: (tenants: Tenant[]) => void;
  setSelectedTenant: (tenant: Tenant) => void;
  clearSelectedTenant: () => void;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

const STORAGE_KEY = 'pivotl_selected_tenant';

interface TenantProviderProps {
  children: ReactNode;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ children }) => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [selectedTenant, setSelectedTenantState] = useState<Tenant | null>(
    null,
  );

  // Load selected organization from localStorage on mount
  useEffect(() => {
    try {
      const storedSelectedTenant = localStorage.getItem(STORAGE_KEY);

      if (storedSelectedTenant) {
        const parsedSelectedTenant = JSON.parse(storedSelectedTenant);
        setSelectedTenantState(parsedSelectedTenant);
      }
    } catch (error) {
      console.error('Error loading selected tenant from localStorage:', error);
    }
  }, []);

  const handleSetTenants = useCallback((newTenants: Tenant[]) => {
    setTenants(newTenants);
  }, []);

  const handleSetSelectedTenant = useCallback((tenant: Tenant) => {
    setSelectedTenantState(tenant);
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(tenant));
    } catch (error) {
      console.error('Error saving selected tenant to localStorage:', error);
    }
  }, []);

  const handleClearSelectedTenant = useCallback(() => {
    setSelectedTenantState(null);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing selected tenant from localStorage:', error);
    }
  }, []);

  const value: TenantContextType = {
    tenants: tenants,
    selectedTenant: selectedTenant,
    setTenants: handleSetTenants,
    setSelectedTenant: handleSetSelectedTenant,
    clearSelectedTenant: handleClearSelectedTenant,
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
};

export const useTenant = (): TenantContextType => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};
