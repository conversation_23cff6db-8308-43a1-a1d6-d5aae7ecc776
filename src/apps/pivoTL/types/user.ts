interface PivotlUserInfo {
  userId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  profilePicture: string | null;
  title: string;
  email: string;
  country: string | null;
  gender: string | null;
  phoneNumberVerified: boolean;
  accountActive: boolean;
  emailVerified: boolean;
  dateOfBirth: string | Date | null;
  userRoles: string[];
  timezone: string | null;
  roleInCompany: string | null;
}

interface TenantInfo {
  id: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  tenantAgentSuite: TenantAgentSuite;
  tenantAgentSuiteClaimer: TenantAgentSuiteClaimer;
  plan: Plan;
  subscriptionStatus: string; // Define a union type if known: "ACTIVE" | "INACTIVE" | "EXPIRED" | "TRIAL"
  trialExpiryDate: string | null;
  subscriptionExpiryDate: string | null;
}

interface TenantAgentSuite {
  agentSuiteName: string;
  agentSuiteKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
  availableAgents: Agent[];
  enabled: boolean;
}

interface Agent {
  agentName: string;
  agentKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
  roles: string[];
  categories: string[];
  enabled: boolean;
}

interface TenantAgentSuiteClaimer {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  title: string | null;
  email: string;
  gender: string | null;
  country: string | null;
  emailVerified: boolean;
  profilePicture: string | null;
  dateOfBirth: string | null;
  accountActive: boolean;
  phoneNumberVerified: boolean;
  userRoles: string[]; // Define a union type if known: "USER" | "ADMIN" | etc.
  timezone: string | null;
  roleInCompany: string | null;
}

interface Plan {
  id: string;
  subscriptionType: string; // Define a union type if known: "Free" | "Basic" | "Premium" | "Enterprise"
  dollarRate: string;
}

interface PivotlUserBasicInfoPayload {
  userInfo: PivotlUserInfo;
  tenants: TenantInfo[];
}

export type { PivotlUserInfo, TenantInfo, PivotlUserBasicInfoPayload };
