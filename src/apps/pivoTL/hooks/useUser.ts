import { useKeycloak } from '@react-keycloak/web';
import { useGetPivotlUserApi } from '../services/upivotalAgenticService';
import { useQuery } from '@tanstack/react-query';
import { GET_USER_QUERY } from '@/utils/queryKeys';
import { isDevEnvironment } from '@/features/UserOnboarding/utils/helper';
import { PivotlUserBasicInfoPayload } from '../types/user';

export const useGetPivotlUser = <T extends PivotlUserBasicInfoPayload>(
  options = {},
) => {
  const { initialized, keycloak } = useKeycloak();
  const getUser = useGetPivotlUserApi();

  return useQuery([GET_USER_QUERY], () => getUser<T>(), {
    ...options,
    select: data => {
      const {
        userInfo: { dateOfBirth, ...userRest },
        ...others
      } = data;

      const newData = {
        ...others,
        userInfo: {
          ...userRest,
          dateOfBirth: dateOfBirth && new Date(dateOfBirth),
        },
      };
      return newData;
    },
    ...(isDevEnvironment()
      ? {}
      : { enabled: !!initialized && !!keycloak.authenticated }),
  });
};
