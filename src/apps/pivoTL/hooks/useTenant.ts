import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  useUpdateTenantInfoApi,
  UpdateTenantInfoRequest,
} from '../services/tenantService';
import { GET_USER_QUERY } from '../../../utils/queryKeys';

export const useUpdateTenantInfo = (options = {}) => {
  const queryClient = useQueryClient();
  const updateTenantInfoApi = useUpdateTenantInfoApi();

  return useMutation({
    mutationFn: (payload: UpdateTenantInfoRequest) =>
      updateTenantInfoApi(payload),
    onSuccess: () => {
      // Invalidate and refetch user data to get updated tenant info
      queryClient.invalidateQueries([GET_USER_QUERY]);
    },
    ...options,
  });
};
