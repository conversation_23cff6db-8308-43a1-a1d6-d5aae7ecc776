import { lazy } from 'react';
import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { Navigate } from 'react-router-dom';
import { ROUTES, ROUTE_PATHS } from '../../constants/routes';

// Lazy load pages for better performance
const PivotlHomePage = withSuspense(
  lazy(() => import('../../pages/PivotlHomePage')),
);

const AgentDetailsPage = withSuspense(
  lazy(() => import('../../pages/PivotlAiAgentsPage/AgentDetailsPage')),
);

// Dashboard Pages (commented out unused imports)
// const ScyraDashboardPage = withSuspense(
//   lazy(() => import('../../pages/PivotlDashboardPage/scyra')),
// );

const SetoDashboard = withSuspense(
  lazy(() => import('../../pages/PivotlDashboardPage/seto')),
);

const LioraDashboard = withSuspense(
  lazy(() => import('../../pages/PivotlDashboardPage/liora')),
);

// Scyra Sub-pages
const ConversationQualityPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlDashboardPage/scyra/ConversationQualityPage'),
  ),
);

const LiveMessageFeedPage = withSuspense(
  lazy(
    () => import('../../pages/PivotlDashboardPage/scyra/LiveMessageFeedPage'),
  ),
);

const AgentActivityLogPage = withSuspense(
  lazy(
    () => import('../../pages/PivotlDashboardPage/scyra/AgentActivityLogPage'),
  ),
);

const AtRiskAccountsPage = withSuspense(
  lazy(
    () => import('../../pages/PivotlDashboardPage/scyra/AtRiskAccountsPage'),
  ),
);

const NextActionsPage = withSuspense(
  lazy(() => import('../../pages/PivotlDashboardPage/scyra/NextActionsPage')),
);

// AI Agents Dashboard Pages
const AIAgentsDashboardPage = withSuspense(
  lazy(() => import('../../pages/PivotlAiAgentsPage')),
);

const AgentSuiteDetailPage = withSuspense(
  lazy(() => import('../../pages/PivotlAiAgentsPage/AgentSuiteDetailPage')),
);

const AgentActivationPage = withSuspense(
  lazy(() => import('../../pages/PivotlAiAgentsPage/AgentActivationPage')),
);

// Dashboard pages
const DashboardPage = withSuspense(
  lazy(() => import('../../pages/PivotlDashboardPage')),
);

const ScyraDashboard = withSuspense(
  lazy(() => import('../../pages/PivotlDashboardPage/SetIQ')),
);

const CollectorPerformanceDetailPage = withSuspense(
  lazy(
    () =>
      import(
        '../../pages/PivotlDashboardPage/SetIQ/CollectorPerformanceDetailPage'
      ),
  ),
);

const DailyInsightsDetailPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlDashboardPage/SetIQ/DailyInsightsDetailPage'),
  ),
);

// Business Stack Dashboard Pages
const BusinessStackDashboardPage = withSuspense(
  lazy(() => import('../../pages/PivotlBusinessStackPage')),
);

// Knowledge Base Dashboard Pages
const KnowledgeBaseDashboardPage = withSuspense(
  lazy(() => import('../../pages/PivotlKnowledgeBasePage')),
);

const KnowledgeBaseCategoryPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlKnowledgeBasePage/KnowledgeBaseCategoryPage'),
  ),
);

const KnowledgeBaseItemPage = withSuspense(
  lazy(
    () => import('../../pages/PivotlKnowledgeBasePage/KnowledgeBaseItemPage'),
  ),
);

// Settings Page
const PivotlSettingsPage = withSuspense(
  lazy(() => import('../../pages/PivotlSettingsPage')),
);

// Seto Sub-pages
const ActiveSettlementsPage = withSuspense(
  lazy(
    () =>
      import(
        '../../pages/PivotlDashboardPage/seto/ActiveSettlementDecisionsPage'
      ),
  ),
);

const PolicyThresholdPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlDashboardPage/seto/PolicyThresholdMonitorPage'),
  ),
);

const IntelligenceSnapshotPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlDashboardPage/seto/IntelligenceSnapshotPage'),
  ),
);

const ExceptionQueuePage = withSuspense(
  lazy(() => import('../../pages/PivotlDashboardPage/seto/ExceptionQueuePage')),
);

const HandoffLogPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlDashboardPage/seto/SetoScyraHandoffLogPage'),
  ),
);

// Liora Sub-pages
const AgentInteractionsPage = withSuspense(
  lazy(
    () => import('../../pages/PivotlDashboardPage/liora/AgentInteractionsPage'),
  ),
);

const DocumentAutomationPage = withSuspense(
  lazy(
    () =>
      import(
        '../../pages/PivotlDashboardPage/liora/DocumentAutomationSummaryPage'
      ),
  ),
);

const PipelineTrackerPage = withSuspense(
  lazy(
    () =>
      import(
        '../../pages/PivotlDashboardPage/liora/DocumentPipelineTrackerPage'
      ),
  ),
);

const IssueExceptionPage = withSuspense(
  lazy(
    () =>
      import('../../pages/PivotlDashboardPage/liora/IssueExceptionTrackerPage'),
  ),
);

const TemplateManagementPage = withSuspense(
  lazy(
    () =>
      import(
        '../../pages/PivotlDashboardPage/liora/TemplateManagementPanelPage'
      ),
  ),
);

export const PivotlShellRoutes = [
  {
    index: true,
    element: <PivotlHomePage />,
  },
  {
    path: ROUTE_PATHS.PIVOTL_AGENTS,
    element: <AgentDetailsPage />,
  },
  {
    path: '*',
    element: <Navigate to={ROUTES.PIVOTL_HOME} />,
  },
];

// Separate dashboard routes that use only DashboardShell (no PivotlNavbar/Footer)
export const PivotlDashboardRoutes = [
  {
    index: true,
    element: <Navigate to={ROUTES.DASHBOARD_SELECT_AGENT} />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_SELECT_AGENT,
    element: <DashboardPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_AI_AGENTS,
    element: <AIAgentsDashboardPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_AGENT_SUITE,
    element: <AgentSuiteDetailPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_AGENT_ACTIVATION,
    element: <AgentActivationPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_AGENT_ACTIVATION_SUITE,
    element: <AgentActivationPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_AGENT_ACTIVATION_AGENT,
    element: <AgentActivationPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD,
    element: <ScyraDashboard />,
    children: [
      {
        path: 'collector-performance',
        element: <CollectorPerformanceDetailPage />,
      },
      {
        path: 'daily-insights',
        element: <DailyInsightsDetailPage />,
      },
      {
        path: ROUTE_PATHS.SCYRA_CONVERSATION_QUALITY,
        element: <ConversationQualityPage />,
      },
      {
        path: ROUTE_PATHS.SCYRA_LIVE_MESSAGE_FEED,
        element: <LiveMessageFeedPage />,
      },
      {
        path: ROUTE_PATHS.SCYRA_AGENT_ACTIVITY_LOG,
        element: <AgentActivityLogPage />,
      },
      {
        path: ROUTE_PATHS.SCYRA_AT_RISK_ACCOUNTS,
        element: <AtRiskAccountsPage />,
      },
      {
        path: ROUTE_PATHS.SCYRA_NEXT_ACTIONS,
        element: <NextActionsPage />,
      },
    ],
  },
  {
    path: ROUTE_PATHS.DASHBOARD_ANALYTICS_SETO,
    element: <SetoDashboard />,
    children: [
      {
        path: ROUTE_PATHS.SETO_ACTIVE_SETTLEMENTS,
        element: <ActiveSettlementsPage />,
      },
      {
        path: ROUTE_PATHS.SETO_POLICY_THRESHOLD,
        element: <PolicyThresholdPage />,
      },
      {
        path: ROUTE_PATHS.SETO_INTELLIGENCE_SNAPSHOT,
        element: <IntelligenceSnapshotPage />,
      },
      {
        path: ROUTE_PATHS.SETO_EXCEPTION_QUEUE,
        element: <ExceptionQueuePage />,
      },
      {
        path: ROUTE_PATHS.SETO_HANDOFF_LOG,
        element: <HandoffLogPage />,
      },
    ],
  },
  {
    path: ROUTE_PATHS.DASHBOARD_ANALYTICS_LIORA,
    element: <LioraDashboard />,
    children: [
      {
        path: ROUTE_PATHS.LIORA_AGENT_INTERACTIONS,
        element: <AgentInteractionsPage />,
      },
      {
        path: ROUTE_PATHS.LIORA_DOCUMENT_AUTOMATION,
        element: <DocumentAutomationPage />,
      },
      {
        path: ROUTE_PATHS.LIORA_PIPELINE_TRACKER,
        element: <PipelineTrackerPage />,
      },
      {
        path: ROUTE_PATHS.LIORA_ISSUE_EXCEPTION,
        element: <IssueExceptionPage />,
      },
      {
        path: ROUTE_PATHS.LIORA_TEMPLATE_MANAGEMENT,
        element: <TemplateManagementPage />,
      },
    ],
  },
  {
    path: ROUTE_PATHS.DASHBOARD_BUSINESS_STACK,
    element: <BusinessStackDashboardPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_BUSINESS_STACK_WITH_APP,
    element: <BusinessStackDashboardPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_KNOWLEDGE_BASE,
    element: <KnowledgeBaseDashboardPage />,
  },
  {
    path: ROUTE_PATHS.KNOWLEDGE_BASE_CATEGORY,
    element: <KnowledgeBaseCategoryPage />,
  },
  {
    path: ROUTE_PATHS.KNOWLEDGE_BASE_ITEM,
    element: <KnowledgeBaseItemPage />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_SETTINGS + '/*',
    element: <PivotlSettingsPage />,
  },
];
