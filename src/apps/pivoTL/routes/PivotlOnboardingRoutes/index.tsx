import { lazy } from 'react';
import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { ROUTE_PATHS } from '../../constants/routes';

// Lazy load onboarding pages
const PivotlSignupPage = withSuspense(
  lazy(() => import('../../pages/PivotlSignupPage')),
);

const PivotlLoginPage = withSuspense(
  lazy(() => import('../../pages/PivotlLoginPage')),
);

const PivotlOrganizationSelectionPage = withSuspense(
  lazy(() => import('../../pages/PivotlOrganizationSelectionPage')),
);

const LoginRedirectHandler = withSuspense(
  lazy(() => import('../../components/auth/LoginRedirectHandler')),
);

export const PivotlOnboardingRoutes = [
  {
    path: ROUTE_PATHS.PIVOTL_SIGNUP,
    element: <PivotlSignupPage />,
  },
  {
    path: ROUTE_PATHS.PIVOTL_LOGIN,
    element: <PivotlLoginPage />,
  },
  {
    path: ROUTE_PATHS.PIVOTL_REDIRECT_HANDLER,
    element: <LoginRedirectHandler />,
  },
  {
    path: ROUTE_PATHS.PIVOTL_ORGANIZATION_SELECTION,
    element: <PivotlOrganizationSelectionPage />,
  },
];
