import { publicRequest } from '../../../lib/axios/publicRequest';
import { BASE_URL } from '../../../utils/apiUrls';
import {
  agenticService,
  agenticUserService,
} from '../../../utils/apiServiceControllersRoute';
import {
  usePivotlPrivateRequest,
  usePrivateRequest,
} from '@/lib/axios/usePrivateRequest';
import { getActiveTenantId } from '../context/ActiveTenantContext';

/**
 * Generates a secure session ID using Web Crypto API
 * @returns A 64-character hex string representing a secure session ID
 */
export const generateSecureSessionId = (): string => {
  // Generate 32 random bytes (256 bits) for strong security
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);

  // Convert to hex string
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export interface ChatRequest {
  userMessage: string;
  sessionId: string;
}

export interface ChatResponse {
  message: string;
}

export interface AIAgent {
  agentName: string;
  agentKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
  roles: string[];
  categories: string[];
  enabled: boolean;
}

export interface AIAgentsResponse {
  status: boolean;
  message: string;
  data: {
    aiAgents: AIAgent[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface AgentCategory {
  categoryName: string;
  categoryAlias: string;
}

export interface AgentCategoriesResponse {
  status: boolean;
  message: string;
  data: AgentCategory[];
}

export interface AIAgentSuite {
  agentSuiteName: string;
  agentSuiteKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
  availableAgents: AIAgent[];
  enabled: boolean;
}

export interface AIAgentsSuiteData {
  aiAgentSuites: AIAgentSuite[];
  total: number;
  page: number;
  pageSize: number;
}

export interface AIAgentsSuiteResponse {
  status: boolean;
  message: string;
  data: AIAgentsSuiteData;
}

export const useGetPivotlUserApi = () => {
  // For the /me endpoint, we don't need a tenant ID since it returns user info including tenants
  const axiosInstance = usePivotlPrivateRequest(BASE_URL, '', '');
  const getUser = async <T>(): Promise<T> => {
    const res = await axiosInstance.current?.get(
      `${agenticUserService}/accounts/me`,
      {
        timeout: 10_000_000_000,
      },
    );
    return res?.data?.data;
  };
  return getUser;
};

// For other API calls that need tenant context
export const useGetPivotlTenantScopedApi = () => {
  const activeTenantId = getActiveTenantId();
  const axiosInstance = usePivotlPrivateRequest(
    BASE_URL,
    activeTenantId || '',
    '',
  );

  return {
    get: async <T>(url: string, config?: any): Promise<T> => {
      const res = await axiosInstance.current?.get(url, config);
      return res?.data?.data;
    },
    post: async <T>(url: string, data?: any, config?: any): Promise<T> => {
      const res = await axiosInstance.current?.post(url, data, config);
      return res?.data?.data;
    },
    put: async <T>(url: string, data?: any, config?: any): Promise<T> => {
      const res = await axiosInstance.current?.put(url, data, config);
      return res?.data?.data;
    },
    delete: async <T>(url: string, config?: any): Promise<T> => {
      const res = await axiosInstance.current?.delete(url, config);
      return res?.data?.data;
    },
  };
};

// Hooks for AI agents API
const AGENTS_ENDPOINT = `${agenticService}/ai-agents/public`;

export const useAIAgentsApi = () => {
  const getAIAgents = async (): Promise<AIAgentsResponse> => {
    try {
      const { data } = await publicRequest(BASE_URL).get(AGENTS_ENDPOINT);
      return (
        data || {
          status: false,
          message: 'No data',
          data: { aiAgents: [], total: 0, page: 1, pageSize: 20 },
        }
      );
    } catch (error) {
      console.error('AI Agents API Error:', error);
      throw new Error('Failed to fetch AI agents. Please try again.');
    }
  };

  return getAIAgents;
};

export const useAIAgentsCategoriesApi = () => {
  const getAIAgents = async (): Promise<AgentCategoriesResponse> => {
    try {
      const { data } = await publicRequest(BASE_URL).get(
        `${AGENTS_ENDPOINT}/categories`,
      );
      return (
        data || {
          status: false,
          message: 'No data',
          data: [],
        }
      );
    } catch (error) {
      console.error('AI Agents Categories API Error:', error);
      throw new Error(
        'Failed to fetch AI agents categories. Please try again.',
      );
    }
  };

  return getAIAgents;
};

export const useAIAgentSuiteApi = () => {
  const getAIAgentsSuite = async (): Promise<AIAgentsSuiteResponse> => {
    try {
      const { data } = await publicRequest(BASE_URL).get(
        `${agenticService}/ai-agent-suites/public`,
      );
      return (
        data || {
          status: false,
          message: 'No data',
          data: { aiAgentSuites: [], total: 0, page: 1, pageSize: 20 },
        }
      );
    } catch (error) {
      console.error('AI Agents Suite API Error:', error);
      throw new Error('Failed to fetch AI agents suite. Please try again.');
    }
  };

  return getAIAgentsSuite;
};

export const useClaimAgentSuiteApi = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const claimAgentsSuite = async (agentSuiteKey: string): Promise<any> => {
    try {
      const res = await axiosInstance.current?.post(
        `${agenticUserService}/tenants/claim-agent-suite`,
        {
          data: agentSuiteKey,
        },
      );
      return (
        res?.data || {
          status: false,
          message: 'No data',
        }
      );
    } catch (error: unknown) {
      console.error('Claiming Agent Suite API Error:', error);

      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error
          ? (error as any).response?.data?.message
          : error instanceof Error
            ? error.message
            : 'Failed to claim agent suite. Please try again.';

      throw new Error(errorMessage);
    }
  };

  return claimAgentsSuite;
};

export const useChatWithRegisApi = () => {
  const chatWithRegis = async (payload: ChatRequest): Promise<string> => {
    try {
      const res = await publicRequest(BASE_URL)?.post(
        `${agenticService}/ai/regis/chat`,
        payload,
      );

      // API returns plain text format
      return res?.data || '';
    } catch (error) {
      console.error('Chat API Error:', error);
      throw new Error('Failed to communicate with Regis. Please try again.');
    }
  };

  return chatWithRegis;
};

// Custom API route handler for ai/react integration
export const createStreamingChatHandler = () => {
  return async (req: any) => {
    try {
      const { messages, sessionId } = await req.json();
      const lastMessage = messages[messages.length - 1];

      // Use the existing API but create a streaming response
      const response = await publicRequest(BASE_URL)?.post(
        `${agenticService}/ai/regis/chat`,
        {
          userMessage: lastMessage.content,
          sessionId,
        },
      );

      const text = response?.data || '';

      // Create a streaming response by chunking the text
      const stream = new ReadableStream({
        start(controller) {
          const words = text.split(' ');
          let index = 0;

          const sendWord = () => {
            if (index < words.length) {
              const chunk = index === 0 ? words[index] : ' ' + words[index];
              controller.enqueue(new TextEncoder().encode(chunk));
              index++;
              setTimeout(sendWord, 50); // Adjust speed as needed
            } else {
              controller.close();
            }
          };

          sendWord();
        },
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
        },
      });
    } catch (error) {
      console.error('Streaming Chat Handler Error:', error);
      return new Response(
        'Failed to communicate with Regis. Please try again.',
        {
          status: 500,
        },
      );
    }
  };
};
