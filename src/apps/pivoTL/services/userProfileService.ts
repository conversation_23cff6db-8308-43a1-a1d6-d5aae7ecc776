import { usePrivateRequest } from '../../../lib/axios/usePrivateRequest';
import { BASE_URL } from '../../../utils/apiUrls';
import { agenticUserService } from '../../../utils/apiServiceControllersRoute';
import { useQuery } from '@tanstack/react-query';

export interface UpdateUserInfoRequest {
  firstname: string;
  lastname: string;
  gender?: string;
  country?: string;
  title?: string;
  timezone?: string;
  roleInCompany?: string;
}

export interface UpdateAvatarRequest {
  avatar: string; // base64 encoded image or file path
}

export interface UserProfileResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  title: string;
  email: string;
  gender: string;
  country: string;
  emailVerified: boolean;
  profilePicture: string;
  dateOfBirth: string;
  accountActive: boolean;
  phoneNumberVerified: boolean;
  userRoles: string[];
  timezone: string;
  roleInCompany: string;
}

export interface ApiResponse<T> {
  status: boolean;
  message: string;
  data: T;
}

export const useUpdateUserInfo = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const updateUserInfo = async (
    payload: UpdateUserInfoRequest,
  ): Promise<ApiResponse<UserProfileResponse>> => {
    try {
      const res = await axiosInstance.current?.patch(
        `${agenticUserService}/accounts/update-user-info`,
        payload,
      );

      if (!res?.data?.status) {
        throw new Error(
          res?.data?.message || 'Failed to update user information',
        );
      }

      return res.data;
    } catch (error: any) {
      // Extract meaningful error message from API response
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Failed to update user information';
      throw new Error(errorMessage);
    }
  };

  return updateUserInfo;
};

export const useUpdateAvatar = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  const updateAvatar = async (
    payload: UpdateAvatarRequest,
  ): Promise<ApiResponse<string>> => {
    try {
      const res = await axiosInstance.current?.put(
        `${agenticUserService}/accounts/update-avatar`,
        payload,
      );

      if (!res?.data?.status) {
        throw new Error(res?.data?.message || 'Failed to update avatar');
      }

      return res.data;
    } catch (error: any) {
      // Extract meaningful error message from API response
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Failed to update avatar';
      throw new Error(errorMessage);
    }
  };

  return updateAvatar;
};

export const useGetUserFunctions = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);
  return useQuery({
    queryKey: ['user-functions'],
    queryFn: async () => {
      const response = await axiosInstance.current?.get(
        `${agenticUserService}/users/user-functions`,
      );
      if (!response?.data?.status) {
        throw new Error(
          response?.data?.message || 'Failed to fetch user functions',
        );
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};
