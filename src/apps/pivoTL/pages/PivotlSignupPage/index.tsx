import { useRegisChat } from '../../hooks/useRegisChat';
import { RegisChatInterface } from '../../components/chat/RegisChatInterface';
import { ChatInput } from '../../components/chat/ChatInput';
import { VerificationCodeInput } from '../../components/chat/VerificationCodeInput';
import { useState, useMemo } from 'react';
import { ArrowUpFromDot, Check, X, Eye, EyeOff } from 'lucide-react';
import PivotlLogo from '../../components/ui/PivotlLogo';
import { eclipse, halfEclipse } from '../../assets/images';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

interface PasswordValidation {
  minLength: boolean;
  hasLowercase: boolean;
  hasUppercase: boolean;
  hasDigit: boolean;
  hasSpecialChar: boolean;
}

const validatePassword = (password: string): PasswordValidation => {
  return {
    minLength: password.length >= 8,
    hasLowercase: /[a-z]/.test(password),
    hasUppercase: /[A-Z]/.test(password),
    hasDigit: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };
};

const PasswordValidationRules = ({ password }: { password: string }) => {
  const validation = validatePassword(password);

  const rules = [
    {
      key: 'minLength',
      label: 'At least 8 characters',
      valid: validation.minLength,
    },
    {
      key: 'hasLowercase',
      label: 'One lowercase letter',
      valid: validation.hasLowercase,
    },
    {
      key: 'hasUppercase',
      label: 'One uppercase letter',
      valid: validation.hasUppercase,
    },
    { key: 'hasDigit', label: 'One digit', valid: validation.hasDigit },
    {
      key: 'hasSpecialChar',
      label: 'One special character',
      valid: validation.hasSpecialChar,
    },
  ];

  return (
    <div className="mt-2 space-y-1 text-gray-600">
      <p className="text-xs">Password must contain:</p>
      {rules.map(rule => (
        <div key={rule.key} className="flex items-center space-x-2">
          {rule.valid ? (
            <Check className="h-3 w-3 text-successTwo" />
          ) : (
            <X className="h-3 w-3 text-red-500" />
          )}
          <span
            className={`text-xs ${rule.valid ? 'text-successTwo' : 'text-red-600'}`}
          >
            {rule.label}
          </span>
        </div>
      ))}
    </div>
  );
};

const SignupFormSidebar = ({
  state,
  sendMessage,
  stepConfig,
  inputValue,
  setInputValue,
  password,
}: any) => {
  const [showPassword, setShowPassword] = useState(false);
  const handleSubmit = () => {
    if (inputValue.trim()) {
      sendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const renderFormContent = () => {
    switch (state.currentStep) {
      case 'firstName':
        return (
          <div className="space-y-4">
            <div className="relative">
              <input
                type={stepConfig.type}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder={stepConfig.placeholder}
              />
            </div>
            <div className="py-2">
              <hr className="border-primary" />
            </div>
            <div className="flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={!inputValue.trim() || state.isLoading}
                className="flex w-fit items-center gap-2 rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
              >
                <span>Proceed</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          </div>
        );

      case 'lastName':
        return (
          <div className="space-y-4">
            {/* Show collected first name */}
            {state.onboardingData.firstName && (
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  First Name
                </label>
                <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                  {state.onboardingData.firstName}
                </div>
              </div>
            )}
            <div className="relative">
              <input
                type={stepConfig.type}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder={stepConfig.placeholder}
              />
            </div>
            <div className="py-2">
              <hr className="border-primary" />
            </div>
            <div className="flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={!inputValue.trim() || state.isLoading}
                className="flex w-fit items-center gap-2 rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
              >
                <span>Proceed</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          </div>
        );

      case 'email':
        return (
          <div className="space-y-4">
            {/* Show collected data */}
            {state.onboardingData.firstName && (
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  First Name
                </label>
                <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                  {state.onboardingData.firstName}
                </div>
              </div>
            )}
            {state.onboardingData.lastName && (
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Last Name
                </label>
                <div className="rounded-md border border-grayFifteen bg-gray-50 px-3 py-2 text-sm text-blackTwo">
                  {state.onboardingData.lastName}
                </div>
              </div>
            )}
            <div className="relative">
              <input
                type={stepConfig.type}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder={stepConfig.placeholder}
              />
            </div>
            <div className="py-2">
              <hr className="border-primary" />
            </div>
            <div className="flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={!inputValue.trim() || state.isLoading}
                className="flex w-fit items-center gap-2 rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
              >
                <span>Proceed</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          </div>
        );

      case 'verification':
        return (
          <div className="space-y-4">
            <VerificationCodeInput
              email={state.onboardingData.email}
              onResend={() =>
                sendMessage(
                  'I did not get the verification code, please resend it.',
                )
              }
              onComplete={code => sendMessage(code)}
            />
          </div>
        );

      case 'password':
        return (
          <div className="space-y-4">
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={state.isLoading}
                className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 pr-10 text-sm text-blackTwo transition-all focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder={stepConfig.placeholder}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-primary"
                disabled={state.isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            <PasswordValidationRules password={password} />
            <div className="py-2">
              <hr className="border-primary" />
            </div>
            <div className="flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={!inputValue.trim() || state.isLoading}
                className="flex w-fit items-center gap-2 rounded-md border border-primary bg-lightOrangeTwo px-6 py-2 font-medium text-blue-midnight transition-colors hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
              >
                <span>Proceed</span>
                <ArrowUpFromDot className="w-5 rotate-90" />
              </button>
            </div>
          </div>
        );

      case 'completed':
        return (
          <div className="text-center">
            <div className="rounded-lg border border-peachTwo bg-white px-6 py-12">
              <h3 className="mb-2 text-lg font-semibold text-primary">
                Account Created Successfully!
              </h3>
              <p className="text-sm text-blackTwo">
                Redirecting you to the dashboard...
              </p>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center text-gray-500">
            <p className="mb-4">Getting started...</p>
            <Spinner />
          </div>
        );
    }
  };

  return (
    <div className="relative flex h-screen flex-col justify-center overflow-hidden rounded bg-gradient-to-br from-orange-50/50 to-orange-100 p-6 font-inter">
      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />

      <div className="relative z-10 max-h-full overflow-y-auto rounded-2xl bg-white p-6 shadow-sm">
        {renderFormContent()}
      </div>
    </div>
  );
};

export const PivotlSignupPage = () => {
  const { state, sendMessage } = useRegisChat('signup');
  const [password, setPassword] = useState('');
  const [currentValue, setCurrentValue] = useState('');

  // Get current step configuration
  const getCurrentStepConfig = () => {
    switch (state.currentStep) {
      case 'firstName':
        return {
          placeholder: 'Enter your first name',
          chatPlaceholder: 'Type your first name here...',
          type: 'text' as const,
        };
      case 'lastName':
        return {
          placeholder: 'Enter your last name',
          chatPlaceholder: 'Type your last name here...',
          type: 'text' as const,
        };
      case 'email':
        return {
          placeholder: 'Enter your email address',
          chatPlaceholder: 'Type your email address here...',
          type: 'email' as const,
        };
      case 'verification':
        return {
          placeholder: 'Enter verification code',
          chatPlaceholder: 'Type the 6-digit verification code...',
          type: 'text' as const,
        };
      case 'password':
        return {
          placeholder: 'Enter your password',
          chatPlaceholder: 'Type your password here...',
          type: 'password' as const,
          showPasswordToggle: true,
        };
      default:
        return {
          placeholder: '',
          chatPlaceholder: "I'm here — whenever you're ready.",
          type: 'text' as const,
        };
    }
  };

  const stepConfig = getCurrentStepConfig();
  const inputValue = state.currentStep === 'password' ? password : currentValue;
  const setInputValue =
    state.currentStep === 'password' ? setPassword : setCurrentValue;

  // Memoized ChatInput component to prevent focus loss
  const MemoizedChatInput = useMemo(() => {
    return () => (
      <ChatInput
        onSendMessage={sendMessage}
        placeholder={stepConfig.chatPlaceholder}
        disabled={state.isLoading}
      />
    );
  }, [sendMessage, stepConfig.chatPlaceholder, state.isLoading]);

  return (
    <div className="mx-auto max-w-screen-3xl font-inter">
      <div className="grid h-screen grid-cols-1 lg:grid-cols-3">
        {/* LHS - Chat Interface */}
        <div className="relative lg:col-span-2 lg:px-24">
          <div className="sticky top-0 z-20 bg-white pb-6 pt-2">
            <PivotlLogo />
          </div>
          <div className="h-[calc(100vh-100px)] bg-white">
            <RegisChatInterface
              state={state}
              ChatInputComponent={MemoizedChatInput}
            />
          </div>
        </div>

        {/* RHS - Form Sidebar */}
        <div className="lg:col-span-1">
          <SignupFormSidebar
            state={state}
            sendMessage={sendMessage}
            stepConfig={stepConfig}
            inputValue={inputValue}
            setInputValue={setInputValue}
            password={password}
          />
        </div>
      </div>
    </div>
  );
};

export default PivotlSignupPage;
