import { useEffect, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { bgGradient, eclipseShadow } from '../../assets/images';
import { agentSuites as mockAgents } from '../../data/constants';
import { ROUTES } from '../../constants/routes';
import {
  AIAgent,
  AIAgentsSuiteResponse,
  useAIAgentSuiteApi,
} from '../../services/upivotalAgenticService';
// import { usePivotlAuth } from '../../context/PivotlAuthContext';
import AgentSuiteSkeletonLoader from '../../components/ui/AgentSuiteSkeleton';

interface TransformedAgentSuite {
  agentSuiteName: string;
  agentSuiteKey: string;
  description: string;
  roleDescription: string;
  avatar: string;
}

interface TransformedResponse {
  status: boolean;
  message: string;
  data: {
    aiAgentSuites: TransformedAgentSuite[];
    total: number;
    page: number;
    pageSize: number;
  };
}

function transformAgentSuites(response: any): TransformedResponse {
  // Extract the original suite and its available agents
  const originalSuite = response.data.aiAgentSuites[0];
  const availableAgents = originalSuite.availableAgents || [];

  // Create new suites from available agents
  const newSuites = availableAgents.map((agent: AIAgent) => ({
    agentSuiteName: agent.agentName,
    agentSuiteKey: agent.agentKey,
    description: agent.description,
    roleDescription: agent.roleDescription,
    avatar: agent.avatar,
  }));

  const allSuites = [originalSuite, ...newSuites];

  return {
    ...response,
    data: {
      ...response.data,
      aiAgentSuites: allSuites,
      total: allSuites.length, // Update total count
    },
  };
}

export const AgentTeamsSection = () => {
  const [isLoading, setIsLoading] = useState(false);
  // const { isAuthenticated } = usePivotlAuth();

  const [agentSuite, setAgentSuite] = useState<TransformedAgentSuite[]>([]);
  const getAIAgentsSuite = useAIAgentSuiteApi();
  // Load agents suite on component mount
  useEffect(() => {
    const loadAgentsSuite = async () => {
      try {
        setIsLoading(true);
        const response = await getAIAgentsSuite();
        if (response.status && response.data.aiAgentSuites) {
          const transformedResponse = transformAgentSuites(
            response as AIAgentsSuiteResponse,
          );
          setAgentSuite(transformedResponse.data.aiAgentSuites);
        }
      } catch (error) {
        console.error('Error loading AI agents suite:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAgentsSuite();
  }, []);

  return (
    <section className="bg-white py-20">
      {/* Gradient overlay */}
      <div
        className="pointer-events-none absolute inset-0 z-0"
        style={{
          backgroundImage: `url(${bgGradient})`,
          backgroundSize: 'auto',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      <div className="container relative z-10 mx-auto -mt-20 px-6">
        <div
          className="pointer-events-none absolute left-[18%] top-3 z-0 h-72 w-72 bg-no-repeat"
          style={{
            backgroundImage: `url(${eclipseShadow})`,
            backgroundSize: 'contain',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            // transform: 'translate(15%, -30%)',
          }}
        />
        <div className="text-center">
          <h2 className="mb-4 text-center text-3xl font-bold">
            Meet The AI Agent Teams
          </h2>
          <p className="mx-auto mb-12 text-center font-inter text-lg text-gray-600">
            Each PivoTL Agent team is a suite of purpose-trained AI Agents
            designed to own and resolve key enterprise workflows.
          </p>
        </div>

        {/* AI Agents */}
        <div className="flex justify-center">
          <div className="flex max-w-screen-2xl flex-wrap justify-center gap-6 px-4">
            {isLoading ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              agentSuite.map((suite, index) => (
                // <Link
                //   to={
                //     isAuthenticated
                //       ? ROUTES.DASHBOARD_AGENT_SUITE(suite.agentSuiteKey)
                //       : ROUTES.PIVOTL_AGENTS(suite.agentSuiteKey)
                //   }
                <Link
                  to={ROUTES.PIVOTL_AGENTS(suite.agentSuiteKey)}
                  key={index}
                  className={`flex cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md`}
                >
                  <div className="w-[290px]">
                    <img
                      src={suite.avatar}
                      className="h-56 w-full object-cover"
                      alt={suite.agentSuiteName}
                      onError={e => {
                        // Fallback to mock logo if agent avatar fails to load
                        (e.target as HTMLImageElement).src = mockAgents.filter(
                          agent =>
                            agent.id.toLowerCase() ===
                            suite.agentSuiteKey.toLowerCase(),
                        )[0].image;
                      }}
                    />
                    <div className="flex flex-col gap-4 p-4 text-blackOne">
                      <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                        {suite.agentSuiteName}
                      </div>
                      <p className="text-lg font-semibold">
                        {suite.description}
                      </p>
                      <p className="mb-3 font-inter text-darkGray">
                        {suite.roleDescription}
                      </p>
                    </div>
                  </div>
                </Link>
              ))
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
