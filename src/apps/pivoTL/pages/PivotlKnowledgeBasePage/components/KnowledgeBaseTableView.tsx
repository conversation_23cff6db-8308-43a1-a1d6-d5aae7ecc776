import React, { useState } from 'react';
import {
  Search,
  Upload,
  Filter,
  MoreHorizontal,
  Trash2,
  Edit,
  ChevronLeft,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from '@/components/ui/ButtonComponent';
import { ROUTES } from '../../../constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';
import Pagination from '@/apps/pivoTL/components/common/Pagination';

interface KnowledgeBaseItem {
  id: string;
  title: string;
  stage: string;
  targetPersona: string;
  useCase: string;
  owner: string;
  status:
    | 'Awaiting Approval'
    | 'Needs Revision'
    | 'Approved'
    | 'Missing'
    | 'Uploaded';
}

interface KnowledgeBaseTableViewProps {
  category: {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
  };
  onBack: () => void;
  onItemClick: (item: KnowledgeBaseItem) => void;
}

const KnowledgeBaseTableView: React.FC<KnowledgeBaseTableViewProps> = ({
  category,
  onBack,
}) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Mock data based on the screenshots
  const knowledgeBaseItems: KnowledgeBaseItem[] = [
    {
      id: '1',
      title: 'Intro Call Script – B2B SaaS',
      stage: 'Discovery',
      targetPersona: 'IT Director',
      useCase: 'Core SaaS Platform',
      owner: 'John M.',
      status: 'Awaiting Approval',
    },
    {
      id: '2',
      title: 'Objection Handling – Pricing',
      stage: 'Evaluation',
      targetPersona: 'CFO, Procurement',
      useCase: 'Enterprise Licensing',
      owner: 'Maria T.',
      status: 'Needs Revision',
    },
    {
      id: '3',
      title: 'Competitive Battlecards',
      stage: 'Mid-Funnel',
      targetPersona: 'Various',
      useCase: 'Platform vs Competitor',
      owner: 'Raj P.',
      status: 'Approved',
    },
    {
      id: '4',
      title: 'Demo Checklist – Technical',
      stage: 'Demo',
      targetPersona: 'Solutions Engineer',
      useCase: 'Advanced Analytics',
      owner: 'Sasha L.',
      status: 'Missing',
    },
    {
      id: '5',
      title: 'Follow-up Email Templates',
      stage: 'Post-Meeting',
      targetPersona: 'VP, Product',
      useCase: 'AI Add-on',
      owner: 'Diego R.',
      status: 'Uploaded',
    },
  ];

  const getStatusBadgeClass = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'awaiting approval':
        return 'bg-grayTen text-white';
      case 'needs revision':
        return 'bg-warning text-white';
      case 'approved':
        return 'bg-[#23BD33] text-white';
      case 'missing':
        return 'bg-delete text-white';
      case 'uploaded':
        return 'bg-disabled text-white';
      default:
        return 'bg-disabled text-white';
    }
  };

  const filteredItems = knowledgeBaseItems.filter(item => {
    const matchesSearch =
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.targetPersona.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.useCase.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = !selectedFilter || item.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  return (
    <AppContainer className="flex h-full flex-col space-y-4">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="flex items-center text-black hover:text-blackOne"
          >
            <ChevronLeft className="h-4 w-4 md:h-6 md:w-6" />
          </button>
          <h1 className="font-semibold md:text-2xl">{category.title}</h1>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-sm font-bold text-black sm:text-base">
            Total: {filteredItems.length} deals
          </div>
          <div className="flex items-center gap-3">
            <button className="flex h-[38px] w-[52px] items-center justify-center rounded-full border border-grayThirteen px-3 py-2 text-sm text-grayTen hover:text-blackOne">
              <Trash2 className="h-4 w-4" />
            </button>

            <button className="grayTen flex h-[38px] w-[52px] items-center justify-center rounded-full border border-grayThirteen px-3 py-2 text-sm text-grayTen hover:text-blackOne">
              <Edit className="h-4 w-4" />
            </button>

            <button className="grayTen flex h-[38px] items-center justify-center gap-2 rounded-full border border-grayThirteen px-3 py-2 text-sm text-grayTen hover:text-blackOne">
              Upload
              <Upload className="h-4 w-4" />
            </button>

            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="h-[38px] w-64 rounded-full border border-grayThirteen pl-4 pr-9 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <Search className="absolute right-3 top-1/2 h-3 w-3 -translate-y-1/2 transform text-grayThirteen" />
            </div>

            <div className="relative">
              <Button
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className="flex h-9 items-center gap-2 rounded-lg border border-primary bg-white px-3 text-sm text-primary hover:bg-primary hover:text-white"
              >
                <span>Filter</span>
                <Filter className="h-4 w-4" />
              </Button>

              {isFilterOpen && (
                <div className="absolute right-0 top-full z-50 mt-2 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
                  <div className="p-2">
                    <button
                      onClick={() => {
                        setSelectedFilter(null);
                        setIsFilterOpen(false);
                      }}
                      className="w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-100"
                    >
                      All Statuses
                    </button>
                    {[
                      'Awaiting Approval',
                      'Needs Revision',
                      'Approved',
                      'Missing',
                      'Uploaded',
                    ].map(status => (
                      <button
                        key={status}
                        onClick={() => {
                          setSelectedFilter(status);
                          setIsFilterOpen(false);
                        }}
                        className="w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-100"
                      >
                        {status}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full">
          <thead className="border-b border-[#BAB9B9]">
            <tr>
              <th className="p-4 text-left text-xs font-light capitalize text-disabled sm:text-sm xl:text-base">
                Title
              </th>
              <th className="p-4 text-left text-xs font-light capitalize text-disabled sm:text-sm xl:text-base">
                Stage
              </th>
              <th className="p-4 text-left text-xs font-light capitalize text-disabled sm:text-sm xl:text-base">
                Target Persona
              </th>
              <th className="p-4 text-left text-xs font-light capitalize text-disabled sm:text-sm xl:text-base">
                Use Case / Product
              </th>
              <th className="p-4 text-left text-xs font-light capitalize text-disabled sm:text-sm xl:text-base">
                Owner
              </th>
              <th className="p-4 text-left text-xs font-light capitalize text-disabled sm:text-sm xl:text-base">
                Status
              </th>
              <th className="px34text-xs text-left font-light capitalize text-disabled sm:text-sm xl:text-base">
                Options
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-[#BAB9B9] font-inter last:border-b last:border-[#BAB9B9]">
            {filteredItems.map(item => (
              <tr
                onClick={() =>
                  navigate(
                    ROUTES.DASHBOARD_KNOWLEDGE_BASE_ITEM(category.id, item.id),
                  )
                }
                key={item.id}
                className="hover:bg-light-orangeTwo"
              >
                <td className="cursor-pointer px-4 py-4 font-light text-black md:text-base">
                  {item.title}
                </td>
                <td className="cursor-pointer px-4 py-4 text-sm font-light text-black md:text-base">
                  {item.stage}
                </td>
                <td className="cursor-pointer px-4 py-4 text-sm font-light text-black md:text-base">
                  {item.targetPersona}
                </td>
                <td className="cursor-pointer px-4 py-4 text-sm font-light text-black md:text-base">
                  {item.useCase}
                </td>
                <td className="cursor-pointer px-4 py-4 text-sm font-light text-black md:text-base">
                  {item.owner}
                </td>
                <td className="cursor-pointer px-4 py-4 text-sm font-light md:text-base">
                  <span
                    className={`flex h-10 w-[130px] items-center justify-center rounded-full px-2 text-xs font-light ${getStatusBadgeClass(item.status)}`}
                  >
                    {item.status}
                  </span>
                </td>
                <td className="px-4 py-6">
                  <div className="relative">
                    <button className="text-grayTen hover:text-gray-600">
                      <MoreHorizontal className="h-5 w-5 rotate-90" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <Pagination currentPage={1} totalPages={10} onPageChange={() => {}} />
    </AppContainer>
  );
};

export default KnowledgeBaseTableView;
