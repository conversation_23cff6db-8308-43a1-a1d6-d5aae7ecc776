import React, { useState, useRef, useEffect } from 'react';
import { X, Cloud, AlertCircle } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (files: File[]) => void;
  title?: string;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  status: 'uploading' | 'uploaded' | 'error';
  progress?: number;
  error?: string;
}

const UploadModal: React.FC<UploadModalProps> = ({
  isOpen,
  onClose,
  onUpload,
  title = 'Import your file',
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // Handle body scroll lock
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (files: File[]) => {
    const newFiles: UploadedFile[] = files.map((file, index) => ({
      id: `${Date.now()}-${index}`,
      name: file.name,
      size: file.size,
      status: 'uploading',
      progress: 0,
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
    setIsUploading(true);

    // Simulate upload progress
    newFiles.forEach(uploadFile => {
      const interval = setInterval(() => {
        setUploadedFiles(prev =>
          prev.map(f => {
            if (f.id === uploadFile.id) {
              const newProgress = (f.progress || 0) + 10;
              if (newProgress >= 100) {
                clearInterval(interval);
                return { ...f, status: 'uploaded', progress: 100 };
              }
              return { ...f, progress: newProgress };
            }
            return f;
          }),
        );
      }, 200);

      // Simulate some files having errors
      if (Math.random() > 0.7) {
        setTimeout(() => {
          setUploadedFiles(prev =>
            prev.map(f =>
              f.id === uploadFile.id
                ? {
                    ...f,
                    status: 'error',
                    error:
                      'This document is not supported. please delete and upload another file.',
                  }
                : f,
            ),
          );
        }, 1500);
      }
    });

    setTimeout(() => setIsUploading(false), 3000);
  };

  const onButtonClick = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // const formatFileSize = (bytes: number): string => {
  //   if (bytes === 0) return '0 Bytes';
  //   const k = 1024;
  //   const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  //   const i = Math.floor(Math.log(bytes) / Math.log(k));
  //   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  // };

  const handleFinalUpload = () => {
    const successfulFiles = uploadedFiles.filter(f => f.status === 'uploaded');
    if (successfulFiles.length > 0) {
      // Convert UploadedFile back to File objects for the callback
      // In a real implementation, you'd have the actual File objects stored
      onUpload([]);
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="mx-4 max-h-[90vh] w-full max-w-2xl overflow-hidden rounded-lg bg-white"
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-blackOne">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6 text-center">
            <h3 className="mb-2 text-lg font-medium text-blackOne">Upload</h3>

            {/* Upload Area */}
            <div
              className={`relative rounded-lg border-2 border-dashed p-8 transition-colors ${
                dragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleChange}
                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.ppt,.pptx"
                className="hidden"
              />

              <div className="text-center">
                <Cloud className="mx-auto mb-4 h-12 w-12 text-primary" />
                <p className="mb-2 text-sm text-gray-600">
                  Drag & drop files or{' '}
                  <button
                    onClick={onButtonClick}
                    className="font-medium text-primary hover:text-primary/80"
                  >
                    Browse
                  </button>
                </p>
                <p className="text-xs text-gray-500">
                  Supported formats: JPEG, PNG, PDF, AI, Word, PPT
                </p>
              </div>
            </div>
          </div>

          {/* Upload Progress */}
          {isUploading && uploadedFiles.length > 0 && (
            <div className="mb-6">
              <div className="mb-2 flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Uploading -{' '}
                  {uploadedFiles.filter(f => f.status === 'uploaded').length}/
                  {uploadedFiles.length} files
                </span>
              </div>

              {uploadedFiles.map(file => (
                <div key={file.id} className="mb-2">
                  <div className="mb-1 flex items-center justify-between">
                    <span className="truncate text-sm text-gray-600">
                      {file.name}
                    </span>
                    <button
                      onClick={() => removeFile(file.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  {file.status === 'uploading' && (
                    <div className="h-1 w-full rounded-full bg-gray-200">
                      <div
                        className="h-1 rounded-full bg-primary transition-all duration-300"
                        style={{ width: `${file.progress || 0}%` }}
                      />
                    </div>
                  )}
                  {file.status === 'error' && (
                    <div className="flex items-center gap-2 text-xs text-red-600">
                      <AlertCircle className="h-3 w-3" />
                      <span>{file.error}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Uploaded Files List */}
          {uploadedFiles.length > 0 && !isUploading && (
            <div className="mb-6">
              <h4 className="mb-3 text-sm font-medium text-gray-700">
                Uploaded
              </h4>
              <div className="space-y-2">
                {uploadedFiles
                  .filter(f => f.status === 'uploaded')
                  .map(file => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between rounded bg-gray-50 p-2"
                    >
                      <span className="truncate text-sm text-gray-700">
                        {file.name}
                      </span>
                      <button
                        onClick={() => removeFile(file.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 border-t border-gray-200 p-6">
          <Button
            variant="outline"
            onClick={onClose}
            className="border border-gray-300 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handleFinalUpload}
            disabled={
              uploadedFiles.filter(f => f.status === 'uploaded').length === 0
            }
            className="bg-primary px-6 py-2 text-sm text-white hover:bg-primary/90 disabled:opacity-50"
          >
            Upload Files
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UploadModal;
