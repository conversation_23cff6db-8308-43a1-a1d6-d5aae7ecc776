import React from 'react';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface KnowledgeBaseCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  onClick: () => void;
}

const KnowledgeBaseCard: React.FC<KnowledgeBaseCardProps> = ({
  title,
  description,
  onClick,
}) => {
  return (
    <div
      onClick={onClick}
      className="group flex cursor-pointer flex-col gap-4 rounded-xl border border-gray-200/40 bg-gray-50/30 p-6 font-spartan transition-all duration-200 hover:border-primary hover:bg-[#FFFAF7] hover:shadow-[0px_4px_16px_0px_#8890A133]"
    >
      {<Icons.FileText className="h-12 w-12 text-subText" />}

      <h3 className="text-base font-semibold text-black">{title}</h3>

      <p className="text-sm leading-relaxed text-subText">{description}</p>
    </div>
  );
};

export default KnowledgeBaseCard;
