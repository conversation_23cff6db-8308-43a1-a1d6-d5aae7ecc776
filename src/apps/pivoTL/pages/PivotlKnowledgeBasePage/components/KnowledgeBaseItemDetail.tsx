import React, { useState } from 'react';
import {
  Chevron<PERSON>eft,
  Trash2,
  Edit,
  Upload,
  X,
  Check<PERSON>ircle,
  XCircle,
} from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import AnimatedModal from '@/apps/pivoTL/components/common/AnimatedModal';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';
import { alertIcon } from '@/apps/pivoTL/assets/icons';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface KnowledgeBaseItem {
  id: string;
  title: string;
  stage: string;
  targetPersona: string;
  useCase: string;
  owner: string;
  status:
    | 'Awaiting Approval'
    | 'Needs Revision'
    | 'Approved'
    | 'Missing'
    | 'Uploaded';
  appointmentDate?: string;
  specialInstructions?: string;
  tags?: string[];
}

interface ActivityLogItem {
  id: string;
  date: string;
  action: string;
  isImportant?: boolean;
}

interface KnowledgeBaseItemDetailProps {
  item: KnowledgeBaseItem;
  onBack: () => void;
  onShowUploadModal?: () => void;
  onShowDeleteModal?: () => void;
}

const KnowledgeBaseItemDetail: React.FC<KnowledgeBaseItemDetailProps> = ({
  item,
  onBack,
  onShowDeleteModal,
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadedFiles] = useState<string[]>([]);
  const [uploadingFiles] = useState<{ name: string; progress: number }[]>([]);
  const [errorFiles] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);

  const getStatusBadgeClass = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'awaiting approval':
        return 'bg-grayTen text-white';
      case 'needs revision':
        return 'bg-warning text-white';
      case 'approved':
        return 'bg-[#23BD33] text-white';
      case 'missing':
        return 'bg-delete text-white';
      case 'uploaded':
        return 'bg-disabled text-white';
      default:
        return 'bg-disabled text-white';
    }
  };

  // Mock activity log data
  const activityLog: ActivityLogItem[] = [
    {
      id: '1',
      date: '17 Nov 2021',
      action: 'Installation or inspection of your thermostat',
      isImportant: true,
    },
    {
      id: '2',
      date: '16 Nov 2021',
      action: 'Installation of the new air conditioning system',
    },
    {
      id: '3',
      date: '16 Nov 2021',
      action: 'Evaluation and removal of the old system',
    },
  ];

  return (
    <div className="flex h-full flex-col font-inter">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="flex items-center text-black hover:text-black"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
            </button>
            <h1 className="font-semibold text-black md:text-lg">
              {item.title}
            </h1>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="flex h-[38px] w-[52px] items-center justify-center rounded-full border border-grayThirteen px-3 py-2 text-sm text-grayTen hover:text-blackOne"
            >
              <Trash2 className="h-4 w-4" />
            </button>

            <button className="grayTen flex h-[38px] w-[52px] items-center justify-center rounded-full border border-grayThirteen px-3 py-2 text-sm text-grayTen hover:text-blackOne">
              <Edit className="h-4 w-4" />
            </button>

            <button
              onClick={() => setShowUploadModal(true)}
              className="grayTen flex h-[38px] items-center justify-center gap-2 rounded-full border border-grayThirteen px-3 py-2 text-sm text-grayTen hover:text-blackOne"
            >
              Replace
              <Upload className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Left Column - Details */}
      <AppContainer className="w-full max-w-[684px] space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-normal text-grayTen">
              Appointment Date
            </label>
            <p className="mt-1 text-sm font-bold text-black md:text-base">
              Nov 17, 2021 08:00
            </p>
          </div>
          <div>
            <label className="text-sm font-normal text-grayTen">Status</label>
            <span
              className={`mt-1 flex h-10 w-[130px] items-center justify-center rounded-full px-2 text-xs font-light ${getStatusBadgeClass(item.status)}`}
            >
              {item.status}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-normal text-grayTen">Owner</label>
            <p className="mt-1 text-sm text-black">John Madonna</p>
          </div>
          <div>
            <label className="text-sm font-normal text-grayTen">
              Target Persona
            </label>
            <p className="mt-1 text-sm text-black">{item.targetPersona}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-normal text-grayTen">Use Case</label>
            <p className="mt-1 text-sm text-black">{item.useCase}</p>
          </div>

          <div>
            <label className="text-sm font-normal text-grayTen">Tags</label>
            <p className="mt-1 text-sm font-bold text-black md:text-base">
              script, SaaS, Cold Call, Discovery
            </p>
          </div>
        </div>

        <div>
          <label className="text-sm font-normal text-grayTen">
            Special Instructions
          </label>
          <p className="fot-medium mt-1 text-sm leading-[30px] text-subText md:text-base">
            At risus viverra adipiscing at in tellus. Blandit massa enim nec dui
            nunc mattis. Lacus vel facilisis volutpat est velit.
          </p>
        </div>

        {/* Activity Log */}
        <div className="flex flex-col gap-5">
          <h1 className="font-bold text-black md:text-lg">Activity Log</h1>
          <div className="space-y-4">
            {activityLog.map(activity => (
              <div key={activity.id} className="flex items-center gap-3">
                <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary">
                  <div className="h-2 w-2 rounded-full bg-white" />
                </div>
                <div>
                  <p className="text-sm leading-[27px] text-grayTen">
                    {activity.date}
                  </p>
                  <p className="text-sm font-medium text-black">
                    {activity.action}
                  </p>
                </div>
              </div>
            ))}
            <button className="text-sm font-medium text-primary hover:text-primary/80">
              Load More
            </button>
          </div>
        </div>
      </AppContainer>

      {/* Delete Confirmation Modal */}
      <AnimatedModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        maxWidth="sm"
        showCloseButton={false}
      >
        <div className="flex flex-col items-center p-8">
          <div className="mb-4 flex flex-col items-center gap-3">
            <img src={alertIcon} alt="delete-demo" className="h-12 w-12" />
            <div>
              <h3 className="text-lg font-semibold text-black">Delete Demo</h3>
            </div>
          </div>

          <div className="mb-6 flex flex-col text-center text-sm text-black md:text-base">
            <span className="font-medium">You're going to delete the </span>
            <span className="font-bold">"{item.title}"</span>
          </div>

          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
              className="rounded-lg border border-gray-300 px-4 py-2 text-sm text-black hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowDeleteConfirm(false);
                onShowDeleteModal?.();
              }}
              className="rounded-lg bg-delete px-4 py-2 text-sm text-white hover:bg-red-700"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>
      </AnimatedModal>

      {/* Upload Modal */}
      <AnimatedModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        showCloseButton={false}
        maxWidth="xl"
      >
        <div className="mx-auto w-full space-y-6 py-10 md:w-[445px]">
          <div className="flex flex-col items-center justify-center gap-6 text-center">
            <div className="flex items-center">
              <h3 className="text-[32px] font-bold text-black">
                Import your file
              </h3>
            </div>
            <div>
              <h3 className="mb-6 text-[22px] font-bold text-black">Upload</h3>

              {/* Drag & Drop Area */}
              <div
                className={`relative w-full rounded-md border-2 border-dashed p-12 transition-colors ${
                  dragActive
                    ? 'border-primary bg-primary/5'
                    : 'border-[#384EB74D] bg-[#F8F8FF]'
                }`}
                onDragEnter={e => {
                  e.preventDefault();
                  setDragActive(true);
                }}
                onDragLeave={e => {
                  e.preventDefault();
                  setDragActive(false);
                }}
                onDragOver={e => e.preventDefault()}
                onDrop={e => {
                  e.preventDefault();
                  setDragActive(false);
                  // Handle file drop logic here
                }}
              >
                <div className="flex flex-col items-center">
                  <Icons.Upload className="mb-6 h-[59.58695602416992px] w-[68.78290557861328px]" />
                  <p className="mb-2 text-base text-black">
                    Drag & drop files or{' '}
                    <button
                      className="font-semibold text-primary underline hover:text-primary/80"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.multiple = true;
                        input.accept =
                          '.jpeg,.jpg,.png,.pdf,.ai,.doc,.docx,.ppt,.pptx';
                        input.onchange = e => {
                          const files = (e.target as HTMLInputElement).files;
                          if (files) {
                            // Handle file selection logic here
                            console.log('Files selected:', files);
                          }
                        };
                        input.click();
                      }}
                    >
                      Browse
                    </button>
                  </p>
                  <p className="text-sm text-grayTen">
                    Supported formats: JPEG, PNG, PDF, AI, Word, PPT
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Upload Progress */}
          {uploadingFiles.length > 0 && (
            <div className="space-y-3">
              <p className="text-sm font-medium text-black">
                Uploading - {uploadingFiles.length}/3 files
              </p>
              {uploadingFiles.map((file, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{file.name}</span>
                    <button className="text-gray-400 hover:text-gray-600">
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="h-2 w-full rounded-full bg-gray-200">
                    <div
                      className="h-2 rounded-full bg-primary transition-all duration-300"
                      style={{ width: `${file.progress}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Uploaded Files */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-3">
              <p className="text-sm font-medium text-black">Uploaded</p>
              {uploadedFiles.map((fileName, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border border-gray-300 p-3"
                >
                  <div className="flex items-center gap-3">
                    <CheckCircle className="text-green-500 h-5 w-5" />
                    <span className="text-sm text-gray-700">{fileName}</span>
                  </div>
                  <button className="text-red-500 hover:text-red-700">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Error Files */}
          {errorFiles.length > 0 && (
            <div className="space-y-3">
              <p className="text-sm font-medium text-black">Error</p>
              {errorFiles.map((fileName, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between rounded-lg border border-red-300 bg-red-50 p-3">
                    <div className="flex items-center gap-3">
                      <XCircle className="h-5 w-5 text-red-500" />
                      <span className="text-sm text-gray-700">{fileName}</span>
                    </div>
                    <button className="text-red-500 hover:text-red-700">
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <p className="text-xs text-red-600">
                    This document is not supported, please delete and upload
                    another file.
                  </p>
                </div>
              ))}
            </div>
          )}

          {/* Upload Button */}
          <button className="h-[48px] w-full rounded-md bg-primary px-4 py-3 text-white hover:bg-primary/90">
            Upload Files
          </button>
        </div>
      </AnimatedModal>
    </div>
  );
};

export default KnowledgeBaseItemDetail;
