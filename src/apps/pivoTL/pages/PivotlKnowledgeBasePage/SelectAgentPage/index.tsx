import React from 'react';
import AgentSelectionLayout from '../../../components/agents/AgentSelectionLayout';
import { useTenant } from '../../../context/TenantContext';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import { knowledgeBaseBg } from '@/apps/pivoTL/assets/images';

const KnowledgeBaseSelectAgentPage: React.FC = () => {
  const navigate = useNavigate();
  const { tenants } = useTenant();

  // Check if user has claimed the specific agent suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return (
      tenants &&
      tenants.length > 0 &&
      tenants.some(tenant => tenant.agentSuiteKey === suiteKey)
    );
  };
  return (
    <AgentSelectionLayout
      title="Adaptive Process Library"
      description="Equip each agent with shared policies and tailored addendums, ensuring accuracy, consistency, and speed."
      bgImage={knowledgeBaseBg}
      pageType="knowledge-base"
      onAgentSuiteClick={suite => {
        if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
          navigate(
            ROUTES.DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE(suite.agentSuiteKey),
          );
        } else {
          navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE);
        }
      }}
    />
  );
};

export default KnowledgeBaseSelectAgentPage;
