import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import KnowledgeBaseItemDetail from './components/KnowledgeBaseItemDetail';
import UploadModal from './components/UploadModal';
import ChatSidebar from '@/apps/pivoTL/components/common/ChatSidebar';
import { ROUTES } from '../../constants/routes';

interface KnowledgeBaseItem {
  id: string;
  title: string;
  stage: string;
  targetPersona: string;
  useCase: string;
  owner: string;
  status:
    | 'Awaiting Approval'
    | 'Needs Revision'
    | 'Approved'
    | 'Missing'
    | 'Uploaded';
}

const KnowledgeBaseItemPage: React.FC = () => {
  const { categoryId, itemId } = useParams<{
    categoryId: string;
    itemId: string;
  }>();
  const navigate = useNavigate();
  const [showUploadModal, setShowUploadModal] = useState(false);

  // Mock data - in a real app, this would come from an API or state management
  const knowledgeBaseItems: KnowledgeBaseItem[] = [
    {
      id: '1',
      title: 'Intro Call Script – B2B SaaS',
      stage: 'Discovery',
      targetPersona: 'IT Director',
      useCase: 'Core SaaS Platform',
      owner: 'John M.',
      status: 'Awaiting Approval',
    },
    {
      id: '2',
      title: 'Objection Handling – Pricing',
      stage: 'Evaluation',
      targetPersona: 'CFO, Procurement',
      useCase: 'Enterprise Licensing',
      owner: 'Maria T.',
      status: 'Needs Revision',
    },
    {
      id: '3',
      title: 'Competitive Battlecards',
      stage: 'Mid-Funnel',
      targetPersona: 'Various',
      useCase: 'Platform vs Competitor',
      owner: 'Raj P.',
      status: 'Approved',
    },
    {
      id: '4',
      title: 'Demo Checklist – Technical',
      stage: 'Demo',
      targetPersona: 'Solutions Engineer',
      useCase: 'Advanced Analytics',
      owner: 'Sasha L.',
      status: 'Missing',
    },
    {
      id: '5',
      title: 'Follow-up Email Templates',
      stage: 'Post-Meeting',
      targetPersona: 'VP, Product',
      useCase: 'AI Add-on',
      owner: 'Diego R.',
      status: 'Uploaded',
    },
  ];

  const item = knowledgeBaseItems.find(item => item.id === itemId);

  const handleBackToTable = () => {
    navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE_CATEGORY(categoryId!));
  };

  const handleUpload = (files: File[]) => {
    console.log('Uploading files:', files);
    setShowUploadModal(false);
  };

  const handleDelete = () => {
    console.log('Deleting item:', itemId);
    // After deletion, navigate back to table
    handleBackToTable();
  };

  if (!item) {
    // Handle invalid item ID
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-2xl font-semibold text-blackOne">
            Item Not Found
          </h2>
          <p className="mb-4 text-gray-600">
            The requested knowledge base item does not exist.
          </p>
          <button
            onClick={handleBackToTable}
            className="rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90"
          >
            Back to Table
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex h-[calc(100vh-150px)] flex-col">
        <div className="flex flex-1 overflow-hidden">
          {/* Main Content - Scrollable */}
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 overflow-y-auto">
              <KnowledgeBaseItemDetail
                item={item}
                onBack={handleBackToTable}
                onShowUploadModal={() => setShowUploadModal(true)}
                onShowDeleteModal={handleDelete}
              />
            </div>

            {/* Chat Sidebar - Fixed height with scrollable messages */}
            <ChatSidebar />
          </div>
        </div>
      </div>

      <UploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUpload}
        title="Replace File"
      />
    </>
  );
};

export default KnowledgeBaseItemPage;
