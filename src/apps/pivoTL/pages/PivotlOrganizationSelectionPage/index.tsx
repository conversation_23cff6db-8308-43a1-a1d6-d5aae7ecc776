import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTenant } from '../../context/TenantContext';
import { ROUTES } from '../../constants/routes';
import { motion } from 'framer-motion';
import { usePivotlAuth } from '../../context/PivotlAuthContext';
import { useActiveTenant } from '../../context/ActiveTenantContext';
import OrganizationSkeletonLoader from '../../components/ui/OrganizationSkeleton';
import PivotlOrganizationHeader from '../../components/layout/PivotlOrganizationHeader';
import { organizationPlaceholderLogo } from '../../assets/images';

const TenantAccessPortalPage: React.FC = () => {
  const navigate = useNavigate();
  const { tenants: organizations, setSelectedTenant: setSelectedOrganization } =
    useTenant();
  const { setActiveTenant, getAllTenants } = useActiveTenant();

  // Use centralized tenant data from PivotlAuthContext via ActiveTenantContext
  const { isLoading: authLoading, isError } = usePivotlAuth();
  const { isActiveTenantLoading } = useActiveTenant();
  const tenants = getAllTenants();

  const isLoading = authLoading || isActiveTenantLoading;

  const handleOrganizationSelect = (organization: any) => {
    setSelectedOrganization(organization);
    // Set active tenant for API calls
    if (organization.tenant) {
      setActiveTenant(organization.tenant);
    }
    // Navigate to dashboard after selection
    navigate(ROUTES.DASHBOARD_BASE);
  };

  console.log('organizations', organizations);
  console.log('tenants', tenants);
  console.log('loading', isLoading);
  console.log('error', isError);

  // Show loading state while tenant data is being fetched
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col bg-white font-inter">
        <PivotlOrganizationHeader />

        {/* Loading content */}
        <main className="flex flex-1 items-center justify-center bg-gray-50 py-16">
          <div className="w-full max-w-4xl px-6">
            <div className="text-center">
              <h1 className="mb-2 text-3xl font-bold text-blackOne">
                Loading your workspaces...
              </h1>
              <p className="mb-12 text-lg text-gray-600">
                Please wait while we fetch your workspace information.
              </p>

              <OrganizationSkeletonLoader count={2} />
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Show error state if there's an error
  if (isError) {
    return (
      <div className="flex min-h-screen flex-col bg-white font-inter">
        <PivotlOrganizationHeader />

        {/* Error content */}
        <main className="flex flex-1 items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mb-4 text-6xl">⚠️</div>
            <h2 className="mb-2 text-2xl font-semibold text-blackOne">
              Unable to Load Workspaces
            </h2>
            <p className="mb-4 text-gray-600">
              We couldn't load your workspace information. Please try again.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="rounded-lg bg-orange-500 px-6 py-2 font-medium text-white transition-colors hover:bg-orange-600"
            >
              Retry
            </button>
          </div>
        </main>
      </div>
    );
  }

  // Show empty state if no tenants
  if (!isLoading && (!tenants || tenants.length === 0)) {
    return (
      <div className="flex min-h-screen flex-col bg-white font-inter">
        <PivotlOrganizationHeader showUpgradeButton={false} />

        {/* Empty state content */}
        <main className="flex flex-1 items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mb-4 text-6xl">🏢</div>
            <h2 className="mb-2 text-2xl font-semibold text-blackOne">
              No Workspaces Available
            </h2>
            <p className="text-gray-600">
              You don't have access to any workspaces at the moment.
            </p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-white font-inter">
      <PivotlOrganizationHeader />

      {/* Main content */}
      <main className="flex flex-1 items-center justify-center bg-gray-50 py-16">
        <div className="w-full max-w-4xl px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <h1 className="mb-2 text-lg font-semibold text-[#0F0006]">
              You're a member of multiple organizations.
            </h1>
            <p className="mb-12 text-sm text-[#0F0006]">
              Please select one to begin.
            </p>

            <div className="flex flex-wrap justify-center gap-8">
              {organizations.map((organization, index) => (
                <motion.div
                  key={organization.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="relative flex flex-col items-center justify-center gap-6 overflow-hidden rounded-lg p-3 text-center shadow-[0px_4px_24px_0px_#0000000F] sm:max-w-[312px] md:p-4"
                >
                  <div className="mx-auto flex h-40 w-full items-center justify-center bg-gradient-to-br from-gray-800 to-blackOne transition-shadow hover:shadow-xl sm:h-[180px] sm:w-[280px]">
                    <img
                      src={organizationPlaceholderLogo}
                      alt="Organization Logo"
                      className="h-16 w-16 object-contain"
                    />
                  </div>

                  <h2 className="max-w-full truncate p-2 text-center text-base font-semibold text-[#0F0006] md:text-lg lg:text-xl">
                    {organization.name}
                  </h2>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleOrganizationSelect(organization)}
                    className="w-[191px] rounded-lg bg-primary px-8 py-3 text-sm font-normal text-white transition-colors hover:bg-primary/90"
                  >
                    Go To Dashboard
                  </motion.button>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </main>
    </div>
  );
};

export default TenantAccessPortalPage;
