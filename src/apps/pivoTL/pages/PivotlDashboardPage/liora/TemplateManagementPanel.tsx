import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface TemplateRowProps {
  templateName: string;
  jurisdiction: string;
  lastUpdated: string;
  lastUsed: string;
  updatedBy: string;
  conflictsDetected: boolean;
}

const TemplateRow: React.FC<TemplateRowProps> = ({
  templateName,
  jurisdiction,
  lastUpdated,
  lastUsed,
  updatedBy,
  conflictsDetected,
}) => (
  <tr>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {templateName}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {jurisdiction}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {lastUpdated}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {lastUsed}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {updatedBy}
    </td>
    <td className="px-6 py-3 text-sm lg:text-[15px]">
      {conflictsDetected ? (
        <span className="inline-flex items-center text-subText">
          1 Missing Clause
        </span>
      ) : (
        <span className="inline-flex items-center text-subText">No</span>
      )}
    </td>
  </tr>
);

const TemplateManagementPanel: React.FC = () => {
  const templates = [
    {
      templateName: 'Settlement_Standard_TX',
      jurisdiction: 'Texas',
      lastUpdated: 'May 15, 2025',
      lastUsed: 'Today',
      updatedBy: 'Liora',
      conflictsDetected: false,
    },
    {
      templateName: 'Complaint_FL_Generic',
      jurisdiction: 'Texas',
      lastUpdated: 'Apr 30, 2025',
      lastUsed: 'Yesterday',
      updatedBy: 'Legal Team',
      conflictsDetected: true,
    },
    {
      templateName: 'Affidavit_CA_Ver1',
      jurisdiction: 'Georgia',
      lastUpdated: 'Jun 1, 2025',
      lastUsed: 'Today',
      updatedBy: 'Liora',
      conflictsDetected: false,
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Template Management Panel"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_LIORA_TEMPLATE_MANAGEMENT}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7] bg-white">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Template Name
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Jurisdiction
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Updated
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Used
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Updated By
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Conflicts Detected
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {templates.map((template, index) => (
                <TemplateRow
                  key={index}
                  templateName={template.templateName}
                  jurisdiction={template.jurisdiction}
                  lastUpdated={template.lastUpdated}
                  lastUsed={template.lastUsed}
                  updatedBy={template.updatedBy}
                  conflictsDetected={template.conflictsDetected}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TemplateManagementPanel;
