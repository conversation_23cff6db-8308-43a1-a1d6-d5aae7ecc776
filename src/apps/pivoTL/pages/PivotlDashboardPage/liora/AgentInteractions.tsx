import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface InteractionRowProps {
  interactionType: string;
  partnerAgent: string;
  description: string;
}

const InteractionRow: React.FC<InteractionRowProps> = ({
  interactionType,
  partnerAgent,
  description,
}) => (
  <tr>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {interactionType}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {partnerAgent}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {description}
    </td>
  </tr>
);

const AgentInteractions: React.FC = () => {
  const interactions = [
    {
      interactionType: 'Document generation request',
      partnerAgent: 'Collin',
      description: 'For commitments that require legal formality',
    },
    {
      interactionType: 'Filing log entry',
      partnerAgent: 'Trail',
      description:
        'Every document created or filed is timestamped and versioned',
    },
    {
      interactionType: 'Legal offer matching',
      partnerAgent: 'Seto',
      description:
        'Liora can reference prior legal outcomes for consistency or precedent',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Agent Interactions (Liora ↔ Collin / Trail / Seto)"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_LIORA_AGENT_INTERACTIONS}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7] bg-white">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Interaction Type
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Partner Agent
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Description
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {interactions.map((interaction, index) => (
                <InteractionRow
                  key={index}
                  interactionType={interaction.interactionType}
                  partnerAgent={interaction.partnerAgent}
                  description={interaction.description}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AgentInteractions;
