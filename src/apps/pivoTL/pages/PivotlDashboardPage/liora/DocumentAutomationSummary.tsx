import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import MetricCard from '../../../components/common/MetricCard';
import { Icons } from '../../../assets/icons/DashboardIcons';
import { ROUTES } from '../../../constants/routes';

const DocumentAutomationSummary: React.FC = () => {
  const metrics = [
    {
      title: 'Documents Generated Today',
      value: '82',
      icon: <Icons.DataRecord className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Agreements Finalized',
      value: '41',
      icon: <Icons.AgreementFinalized className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Court Filings Prepared',
      value: '22',
      icon: <Icons.DocumentStack className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Pending Review (Policy Violation / Incomplete)',
      value: '9',
      icon: <Icons.RiskAnalysis className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Templates Updated',
      value: '3',
      icon: <Icons.Loop className="h-4 w-4" />,
      trend: undefined,
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Document Automation Summary"
        className="mb-6"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_LIORA_DOCUMENT_AUTOMATION}
      />

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            trend={metric.trend}
          />
        ))}
      </div>
    </div>
  );
};

export default DocumentAutomationSummary;
