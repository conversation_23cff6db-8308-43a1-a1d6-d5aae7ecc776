import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface InteractionRowProps {
  interactionType: string;
  partnerAgent: string;
  description: string;
  timestamp: string;
  status: 'Completed' | 'In Progress' | 'Failed';
  requestId: string;
}

const InteractionRow: React.FC<InteractionRowProps> = ({
  interactionType,
  partnerAgent,
  description,
  timestamp,
  status,
  requestId,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'in progress':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {requestId}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {interactionType}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {partnerAgent}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {description}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {timestamp}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(status)}`}
        >
          {status}
        </span>
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <ActionDropdown />
      </td>
    </tr>
  );
};

const AgentInteractionsPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'Collin Interactions', value: 'collin' },
    { id: '2', label: 'Trail Interactions', value: 'trail' },
    { id: '3', label: 'Seto Interactions', value: 'seto' },
    { id: '4', label: 'Completed', value: 'completed' },
    { id: '5', label: 'In Progress', value: 'in_progress' },
    { id: '6', label: 'Failed', value: 'failed' },
  ];

  const interactions = [
    {
      requestId: 'REQ-001',
      interactionType: 'Document generation request',
      partnerAgent: 'Collin',
      description: 'For commitments that require legal formality',
      timestamp: 'June 15, 2025 10:30 AM',
      status: 'Completed' as const,
    },
    {
      requestId: 'REQ-002',
      interactionType: 'Filing log entry',
      partnerAgent: 'Trail',
      description:
        'Every document created or filed is timestamped and versioned',
      timestamp: 'June 15, 2025 10:15 AM',
      status: 'Completed' as const,
    },
    {
      requestId: 'REQ-003',
      interactionType: 'Legal offer matching',
      partnerAgent: 'Seto',
      description:
        'Liora can reference prior legal outcomes for consistency or precedent',
      timestamp: 'June 15, 2025 09:45 AM',
      status: 'In Progress' as const,
    },
    {
      requestId: 'REQ-004',
      interactionType: 'Template validation',
      partnerAgent: 'Collin',
      description:
        'Verification of document template compliance with current regulations',
      timestamp: 'June 15, 2025 09:30 AM',
      status: 'Completed' as const,
    },
    {
      requestId: 'REQ-005',
      interactionType: 'Audit trail creation',
      partnerAgent: 'Trail',
      description:
        'Creating comprehensive audit trail for regulatory compliance',
      timestamp: 'June 15, 2025 09:00 AM',
      status: 'Failed' as const,
    },
    {
      requestId: 'REQ-006',
      interactionType: 'Settlement agreement review',
      partnerAgent: 'Seto',
      description: 'Cross-referencing settlement terms with policy guidelines',
      timestamp: 'June 15, 2025 08:45 AM',
      status: 'Completed' as const,
    },
    {
      requestId: 'REQ-007',
      interactionType: 'Document automation',
      partnerAgent: 'Collin',
      description: 'Automated generation of standard legal documents',
      timestamp: 'June 15, 2025 08:30 AM',
      status: 'In Progress' as const,
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_LIORA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading agent interactions report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-blackOne"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Agent Interactions
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search interactions"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7] bg-white">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Request ID
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Interaction Type
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Partner Agent
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Description
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Timestamp
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {interactions.map((interaction, index) => (
                  <InteractionRow
                    key={index}
                    requestId={interaction.requestId}
                    interactionType={interaction.interactionType}
                    partnerAgent={interaction.partnerAgent}
                    description={interaction.description}
                    timestamp={interaction.timestamp}
                    status={interaction.status}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default AgentInteractionsPage;
