import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface DocumentRowProps {
  documentId: string;
  documentType: string;
  customerId: string;
  status: 'Generated' | 'Finalized' | 'Filed' | 'Pending Review';
  generatedDate: string;
  automationEngine: string;
  accuracy: string;
}

const DocumentRow: React.FC<DocumentRowProps> = ({
  documentId,
  documentType,
  customerId,
  status,
  generatedDate,
  automationEngine,
  accuracy,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'generated':
        return 'text-blue-600 bg-blue-100';
      case 'finalized':
        return 'text-green-600 bg-green-100';
      case 'filed':
        return 'text-purple-600 bg-purple-100';
      case 'pending review':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {documentId}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {documentType}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(status)}`}
        >
          {status}
        </span>
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {generatedDate}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {automationEngine}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {accuracy}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <ActionDropdown />
      </td>
    </tr>
  );
};

const DocumentAutomationSummaryPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 5;

  const filterOptions = [
    { id: '1', label: 'Settlement Agreements', value: 'settlement' },
    { id: '2', label: 'Court Filings', value: 'court' },
    { id: '3', label: 'Affidavits', value: 'affidavit' },
    { id: '4', label: 'Generated Today', value: 'today' },
    { id: '5', label: 'Pending Review', value: 'pending' },
    { id: '6', label: 'High Accuracy', value: 'high_accuracy' },
  ];

  const documents = [
    {
      documentId: 'DOC-2501',
      documentType: 'Settlement Agreement',
      customerId: '#39029',
      status: 'Finalized' as const,
      generatedDate: 'June 15, 2025 10:30 AM',
      automationEngine: 'Liora AI v3.2',
      accuracy: '98.5%',
    },
    {
      documentId: 'DOC-2502',
      documentType: 'Summons + Complaint',
      customerId: '#84401',
      status: 'Pending Review' as const,
      generatedDate: 'June 15, 2025 10:15 AM',
      automationEngine: 'Liora AI v3.2',
      accuracy: '96.2%',
    },
    {
      documentId: 'DOC-2503',
      documentType: 'Affidavit of Debt',
      customerId: '#28331',
      status: 'Filed' as const,
      generatedDate: 'June 15, 2025 09:45 AM',
      automationEngine: 'Liora AI v3.1',
      accuracy: '99.1%',
    },
    {
      documentId: 'DOC-2504',
      documentType: 'Stipulation Agreement',
      customerId: '#78311',
      status: 'Generated' as const,
      generatedDate: 'June 15, 2025 09:30 AM',
      automationEngine: 'Liora AI v3.2',
      accuracy: '97.8%',
    },
    {
      documentId: 'DOC-2505',
      documentType: 'Payment Plan Agreement',
      customerId: '#45678',
      status: 'Finalized' as const,
      generatedDate: 'June 15, 2025 09:00 AM',
      automationEngine: 'Liora AI v3.2',
      accuracy: '98.9%',
    },
    {
      documentId: 'DOC-2506',
      documentType: 'Notice of Default',
      customerId: '#12345',
      status: 'Generated' as const,
      generatedDate: 'June 15, 2025 08:45 AM',
      automationEngine: 'Liora AI v3.1',
      accuracy: '95.7%',
    },
    {
      documentId: 'DOC-2507',
      documentType: 'Cease and Desist Letter',
      customerId: '#67890',
      status: 'Pending Review' as const,
      generatedDate: 'June 15, 2025 08:30 AM',
      automationEngine: 'Liora AI v3.2',
      accuracy: '97.3%',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_LIORA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading document automation report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-blackOne"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Document Automation Summary
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search documents"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7] bg-white">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Document ID
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Document Type
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Customer ID
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Generated Date
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Automation Engine
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Accuracy
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {documents.map((document, index) => (
                  <DocumentRow
                    key={index}
                    documentId={document.documentId}
                    documentType={document.documentType}
                    customerId={document.customerId}
                    status={document.status}
                    generatedDate={document.generatedDate}
                    automationEngine={document.automationEngine}
                    accuracy={document.accuracy}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default DocumentAutomationSummaryPage;
