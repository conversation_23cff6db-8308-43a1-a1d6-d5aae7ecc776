import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface IssueRowProps {
  docId: string;
  issueType: string;
  lastUpdated: string;
  resolutionStatus: string;
  updatedBy: string;
  assignedTo: string;
}

const IssueRow: React.FC<IssueRowProps> = ({
  docId,
  issueType,
  lastUpdated,
  resolutionStatus,
  updatedBy,
  assignedTo,
}) => {
  return (
    <tr className="">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{docId}</td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {issueType}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {lastUpdated}
      </td>
      <td className={`px-6 py-3 text-sm text-subText lg:text-[15px]`}>
        {resolutionStatus}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {updatedBy}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {assignedTo}
      </td>
    </tr>
  );
};

const IssueExceptionTracker: React.FC = () => {
  const issues = [
    {
      docId: 'D1012',
      issueType: 'Incomplete jurisdictional section',
      lastUpdated: 'Liora',
      resolutionStatus: 'Awaiting Human Review',
      updatedBy: 'Liora',
      assignedTo: 'Paralegal',
    },
    {
      docId: 'D1012',
      issueType: 'Attachment mismatch',
      lastUpdated: 'Validator',
      resolutionStatus: 'Sent for re-generation',
      updatedBy: 'Legal Team',
      assignedTo: 'Liora',
    },
    {
      docId: 'D1012',
      issueType: 'Template version conflict',
      lastUpdated: 'Liora',
      resolutionStatus: 'Escalated',
      updatedBy: 'Liora',
      assignedTo: 'Legal Ops',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Issue & Exception Tracker"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_LIORA_ISSUE_EXCEPTION}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7] bg-white">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Doc ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Issue Type
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Updated
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Resolution Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Updated By
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Assigned To
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {issues.map((issue, index) => (
                <IssueRow
                  key={index}
                  docId={issue.docId}
                  issueType={issue.issueType}
                  lastUpdated={issue.lastUpdated}
                  resolutionStatus={issue.resolutionStatus}
                  updatedBy={issue.updatedBy}
                  assignedTo={issue.assignedTo}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default IssueExceptionTracker;
