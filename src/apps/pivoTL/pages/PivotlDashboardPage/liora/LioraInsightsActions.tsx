import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface ActionItemProps {
  action: string;
}

const ActionItem: React.FC<ActionItemProps> = ({ action }) => (
  <div className="flex items-center rounded-lg bg-white p-4">
    <div className="mr-4 flex h-[36px] w-[36px] items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
      <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
    </div>
    <span className="text-xs text-subText sm:text-sm lg:text-[15px]">
      {action}
    </span>
  </div>
);

const LioraInsightsActions: React.FC = () => {
  const actions = [
    '4 Florida complaints are pending signature logic. Consider updating template default.',
    '9% of affidavits missing required clause for California. Flag template for audit.',
    'Auto-generate 12 agreements for commitments confirmed by <PERSON> today.',
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="AI Insights And Suggested Actions"
        className="mb-4"
        showBrowseAll={false}
      />

      <div className="space-y-3">
        {actions.map((action, index) => (
          <ActionItem key={index} action={action} />
        ))}
      </div>
    </div>
  );
};

export default LioraInsightsActions;
