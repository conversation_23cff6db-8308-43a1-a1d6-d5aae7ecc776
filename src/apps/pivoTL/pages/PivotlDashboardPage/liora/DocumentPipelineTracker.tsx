import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { Icons } from '../../../assets/icons/DashboardIcons';
import { ROUTES } from '../../../constants/routes';

interface DocumentRowProps {
  docId: string;
  customerId: string;
  documentType: string;
  status: string;
  jurisdiction: string;
  riskFlags: string;
}

const DocumentRow: React.FC<DocumentRowProps> = ({
  docId,
  customerId,
  documentType,
  status,
  jurisdiction,
  riskFlags,
}) => {
  const getActionButton = (status: string) => {
    const baseClasses =
      'px-4 py-1.5 text-xs font-medium rounded-full text-blackOne flex items-center bg-transparent border border-blackOne ';

    if (status.toLowerCase().includes('finalized')) {
      return (
        <button
          className={`flex items-center gap-1 text-subText ${baseClasses} hover:bg-blue-200`}
        >
          <Icons.Eye className="h-3 w-3" />
          View
        </button>
      );
    } else if (status.toLowerCase().includes('awaiting')) {
      return (
        <button
          className={`flex items-center gap-1 text-subText ${baseClasses} hover:bg-yellow-200`}
        >
          <Icons.EditIcon className="h-3 w-3" />
          Edit
        </button>
      );
    } else if (status.toLowerCase().includes('filed')) {
      return (
        <button
          className={`flex items-center gap-1 text-subText ${baseClasses} hover:bg-green-200`}
        >
          <Icons.AuditIcon className="h-3 w-3" />
          Audit
        </button>
      );
    } else {
      return (
        <button
          className={`flex items-center gap-1 bg-red-100 text-subText ${baseClasses} hover:bg-red-200`}
        >
          <Icons.FixIcon className="h-3 w-3" />
          Fix
        </button>
      );
    }
  };

  return (
    <tr>
      <td className="px-6 py-3 text-sm font-medium text-subText lg:text-[15px]">
        {docId}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {documentType}
      </td>
      <td className={`px-6 py-3 text-sm text-subText lg:text-[15px]`}>
        {status}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {jurisdiction}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {riskFlags}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        {getActionButton(status)}
      </td>
    </tr>
  );
};

const DocumentPipelineTracker: React.FC = () => {
  const documents = [
    {
      docId: 'D1008',
      customerId: '#39029',
      documentType: 'Settlement Agreement',
      status: 'Finalized & Sent',
      jurisdiction: 'Texas',
      riskFlags: '-',
    },
    {
      docId: 'D1012',
      customerId: '#84401',
      documentType: 'Summons + Complaint',
      status: 'Awaiting Human Review',
      jurisdiction: 'Texas',
      riskFlags: 'Missing signature block',
    },
    {
      docId: 'D1017',
      customerId: '#28331',
      documentType: 'Affidavit of Debt',
      status: 'Filed (e-file)',
      jurisdiction: 'Texas',
      riskFlags: '-',
    },
    {
      docId: 'D1022',
      customerId: '#78311',
      documentType: 'Stipulation Agreement',
      status: 'Drafted - Awaiting Approval',
      jurisdiction: 'Georgia',
      riskFlags: 'Late attachment',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Document Pipeline Tracker"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_LIORA_PIPELINE_TRACKER}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7] bg-white">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Doc ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Customer ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Document Type
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Jurisdiction
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Risk Flags
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {documents.map((doc, index) => (
                <DocumentRow
                  key={index}
                  docId={doc.docId}
                  customerId={doc.customerId}
                  documentType={doc.documentType}
                  status={doc.status}
                  jurisdiction={doc.jurisdiction}
                  riskFlags={doc.riskFlags}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DocumentPipelineTracker;
