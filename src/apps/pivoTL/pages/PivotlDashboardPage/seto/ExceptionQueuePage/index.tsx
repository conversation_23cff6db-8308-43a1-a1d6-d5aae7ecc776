import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface ExceptionRowProps {
  customerId: string;
  issueType: string;
  offerAmount: string;
  settlementPercent: string;
  actionRequired: string;
  priority: 'High' | 'Medium' | 'Low';
  dateReported: string;
}

const ExceptionRow: React.FC<ExceptionRowProps> = ({
  customerId,
  issueType,
  offerAmount,
  settlementPercent,
  actionRequired,
  priority,
  dateReported,
}) => {
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {issueType}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {offerAmount}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {settlementPercent}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {actionRequired}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getPriorityColor(priority)}`}
        >
          {priority}
        </span>
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {dateReported}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <ActionDropdown />
      </td>
    </tr>
  );
};

const ExceptionQueuePage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 3;

  const filterOptions = [
    { id: '1', label: 'High Priority', value: 'high_priority' },
    { id: '2', label: 'Medium Priority', value: 'medium_priority' },
    { id: '3', label: 'Low Priority', value: 'low_priority' },
    { id: '4', label: 'Manual Review', value: 'manual_review' },
    { id: '5', label: 'System Issues', value: 'system_issues' },
    { id: '6', label: 'Customer Issues', value: 'customer_issues' },
  ];

  const exceptions = [
    {
      customerId: '#66239',
      issueType: 'Repeated Rejection',
      offerAmount: '$150',
      settlementPercent: '3.0%',
      actionRequired: 'Manual Review',
      priority: 'High' as const,
      dateReported: 'June 15, 2025',
    },
    {
      customerId: '#33491',
      issueType: 'Ledger Conflict (Payment in flight)',
      offerAmount: '$370',
      settlementPercent: '4.4%',
      actionRequired: 'Re-sync w/ Ledger',
      priority: 'High' as const,
      dateReported: 'June 15, 2025',
    },
    {
      customerId: '#78402',
      issueType: 'Senti flagged hostility',
      offerAmount: '$600',
      settlementPercent: '4.6%',
      actionRequired: 'Human Override',
      priority: 'Medium' as const,
      dateReported: 'June 14, 2025',
    },
    {
      customerId: '#45123',
      issueType: 'Documentation Incomplete',
      offerAmount: '$280',
      settlementPercent: '2.8%',
      actionRequired: 'Request Documents',
      priority: 'Low' as const,
      dateReported: 'June 14, 2025',
    },
    {
      customerId: '#89764',
      issueType: 'System Timeout Error',
      offerAmount: '$520',
      settlementPercent: '5.2%',
      actionRequired: 'Retry Processing',
      priority: 'Medium' as const,
      dateReported: 'June 13, 2025',
    },
    {
      customerId: '#12345',
      issueType: 'Customer Dispute',
      offerAmount: '$750',
      settlementPercent: '7.1%',
      actionRequired: 'Legal Review',
      priority: 'High' as const,
      dateReported: 'June 13, 2025',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETO);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading exception queue report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold text-blackOne"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Exception Queue
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search exceptions"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-[#F4F5F7]">
                <tr>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Customer ID
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Issue Type
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Offer Amount
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Settlement %
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Action Required
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Priority
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Date Reported
                  </th>
                  <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {exceptions.map((exception, index) => (
                  <ExceptionRow
                    key={index}
                    customerId={exception.customerId}
                    issueType={exception.issueType}
                    offerAmount={exception.offerAmount}
                    settlementPercent={exception.settlementPercent}
                    actionRequired={exception.actionRequired}
                    priority={exception.priority}
                    dateReported={exception.dateReported}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default ExceptionQueuePage;
