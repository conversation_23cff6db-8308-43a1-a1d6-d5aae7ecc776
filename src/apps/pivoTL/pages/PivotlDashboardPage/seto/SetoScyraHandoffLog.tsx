import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface HandoffRowProps {
  offerId: string;
  decision: string;
  messageSentBy: string;
  sentimentRisk: 'Low' | 'Medium' | 'High';
  escalationTriggered: boolean;
}

const HandoffRow: React.FC<HandoffRowProps> = ({
  offerId,
  decision,
  messageSentBy,
  sentimentRisk,
  escalationTriggered,
}) => {
  return (
    <tr className="">
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {offerId}
      </td>
      <td className={`px-6 py-3 text-sm lg:text-[15px]`}>{decision}</td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {messageSentBy}
      </td>
      <td className={`px-6 py-3 text-sm lg:text-[15px]`}>{sentimentRisk}</td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        {escalationTriggered ? (
          <span className="inline-flex items-center text-subText">Yes</span>
        ) : (
          <span className="inline-flex items-center text-subText">No</span>
        )}
      </td>
    </tr>
  );
};

const SetoScyraHandoffLog: React.FC = () => {
  const handoffs = [
    {
      offerId: 'A1921',
      decision: 'Accepted',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Low' as const,
      escalationTriggered: false,
    },
    {
      offerId: 'B1921',
      decision: 'Countered',
      messageSentBy: 'Scyra',
      sentimentRisk: 'Medium' as const,
      escalationTriggered: false,
    },
    {
      offerId: 'D1921',
      decision: 'Rejected',
      messageSentBy: 'Scyra',
      sentimentRisk: 'High' as const,
      escalationTriggered: true,
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Seto→Scyra Messaging Handoff Log"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SETO_HANDOFF_LOG}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7]">
              <tr>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Offer ID
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Decision
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Message Sent By
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Sentiment Risk
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Escalation Triggered?
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {handoffs.map((handoff, index) => (
                <HandoffRow
                  key={index}
                  offerId={handoff.offerId}
                  decision={handoff.decision}
                  messageSentBy={handoff.messageSentBy}
                  sentimentRisk={handoff.sentimentRisk}
                  escalationTriggered={handoff.escalationTriggered}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SetoScyraHandoffLog;
