import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface ExceptionRowProps {
  customerId: string;
  issueType: string;
  offerAmount: string;
  settlementPercent: string;
  actionRequired: string;
}

const ExceptionRow: React.FC<ExceptionRowProps> = ({
  customerId,
  issueType,
  offerAmount,
  settlementPercent,
  actionRequired,
}) => (
  <tr className="">
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {customerId}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {issueType}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {offerAmount}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {settlementPercent}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
      {actionRequired}
    </td>
  </tr>
);

const ExceptionQueue: React.FC = () => {
  const exceptions = [
    {
      customerId: '#66239',
      issueType: 'Repeated Rejection',
      offerAmount: '$150',
      settlementPercent: '3.0%',
      actionRequired: 'Manual Review',
    },
    {
      customerId: '#33491',
      issueType: 'Ledger Conflict (Payment in flight)',
      offerAmount: '$370',
      settlementPercent: '4.4%',
      actionRequired: 'Re-sync w/ Ledger',
    },
    {
      customerId: '#78402',
      issueType: 'Senti flagged hostility',
      offerAmount: '$600',
      settlementPercent: '4.6%',
      actionRequired: 'Human Override',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Exception Queue (Manager Attention Required)"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SETO_EXCEPTION_QUEUE}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7]">
              <tr>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Customer ID
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Issue Type
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Offer Amount
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Settlement %
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Action Required
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {exceptions.map((exception, index) => (
                <ExceptionRow
                  key={index}
                  customerId={exception.customerId}
                  issueType={exception.issueType}
                  offerAmount={exception.offerAmount}
                  settlementPercent={exception.settlementPercent}
                  actionRequired={exception.actionRequired}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ExceptionQueue;
