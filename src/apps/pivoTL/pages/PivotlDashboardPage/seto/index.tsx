import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, Search, Filter } from 'lucide-react';
import { useLocation, useNavigate, Outlet } from 'react-router-dom';
import { liora<PERSON><PERSON>, scyra<PERSON>ogo } from '../../../assets/images';
import Button from '@/components/ui/ButtonComponent';
import { ROUTES } from '../../../constants/routes';
import SetoExecutiveSummary from './SetoExecutiveSummary';
import ActiveSettlementDecisions from './ActiveSettlementDecisions';
import PolicyThresholdMonitor from './PolicyThresholdMonitor';
import IntelligenceSnapshot from './IntelligenceSnapshot';
import ExceptionQueue from './ExceptionQueue';
import SetoScyraHandoffLog from './SetoScyraHandoffLog';
import SetoManagerActions from './SetoManagerActions';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface DashboardOption {
  id: string;
  name: string;
  path: string;
  icon: string;
}

const SetoDashboard: React.FC = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedDashboard, setSelectedDashboard] =
    useState<DashboardOption | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Check if we're on a nested route
  const isNestedRoute = location.pathname !== ROUTES.DASHBOARD_ANALYTICS_SETO;

  const dashboardOptions: DashboardOption[] = [
    {
      id: 'scyra',
      name: 'Scyra Dashboard',
      path: ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD,
      icon: scyraLogo,
    },
    {
      id: 'seto',
      name: 'Seto Dashboard',
      path: ROUTES.DASHBOARD_ANALYTICS_SETO,
      icon: scyraLogo,
    },
    {
      id: 'liora',
      name: 'Liora Dashboard',
      path: ROUTES.DASHBOARD_ANALYTICS_LIORA,
      icon: lioraLogo,
    },
  ];

  // Determine current dashboard based on URL
  useEffect(() => {
    const currentPath = location.pathname;
    const currentDashboard =
      dashboardOptions.find(option => currentPath.includes(option.id)) ||
      dashboardOptions[1]; // Default to Seto
    setSelectedDashboard(currentDashboard);
  }, [location.pathname]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleDashboardSelect = (dashboard: DashboardOption) => {
    setSelectedDashboard(dashboard);
    setIsDropdownOpen(false);
    navigate(dashboard.path);
  };

  return (
    <>
      {/* Render nested routes or dashboard components */}
      {isNestedRoute ? (
        <Outlet />
      ) : (
        <div className="min-h-screen">
          <AppContainer className="space-y-8">
            {/* Header with Dropdown and Search/Filter */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Custom Dropdown */}
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="flex h-[48px] items-center gap-3 rounded-lg border border-[#718EBF] bg-white px-4 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 md:w-[246px]"
                  >
                    <img
                      src={selectedDashboard?.icon || ''}
                      alt="Dashboard Icon"
                      className="h-6 w-6 sm:h-8 sm:w-8"
                    />
                    <span className="text-sm font-semibold text-blackOne">
                      {selectedDashboard?.name || 'Seto Analytics'}
                    </span>
                    {isDropdownOpen ? (
                      <ChevronUp className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </button>

                  {/* Dropdown Menu */}
                  {isDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute left-10 top-full z-50 mt-1 w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]"
                    >
                      <div className="py-1">
                        {dashboardOptions
                          .filter(option => option.id !== selectedDashboard?.id)
                          .map(option => (
                            <button
                              key={option.id}
                              onClick={() => handleDashboardSelect(option)}
                              className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm font-medium transition-colors hover:bg-gray-50 sm:text-base"
                            >
                              <img
                                src={option.icon}
                                alt="Dashboard Icon"
                                className="h-6 w-6 sm:h-8 sm:w-8"
                              />
                              {option.name}
                            </button>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>

              {/* Search and Filter */}
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
                  <input
                    type="text"
                    placeholder="Search"
                    className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
                  />
                </div>
                <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
                  <span>Filter</span>
                  <Filter className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Executive Summary */}
            <SetoExecutiveSummary />

            {/* Active Settlement Decisions Table */}
            <ActiveSettlementDecisions />

            {/* Policy Threshold Monitor */}
            <PolicyThresholdMonitor />

            {/* Intelligence Snapshot */}
            <IntelligenceSnapshot />

            {/* Exception Queue */}
            <ExceptionQueue />

            {/* Seto→Scyra Messaging Handoff Log */}
            <SetoScyraHandoffLog />

            {/* Suggested Manager Actions */}
            <SetoManagerActions />
          </AppContainer>
        </div>
      )}
    </>
  );
};

export default SetoDashboard;
