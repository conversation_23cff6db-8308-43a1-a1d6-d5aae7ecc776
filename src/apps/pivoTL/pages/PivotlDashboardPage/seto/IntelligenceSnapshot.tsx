import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { Icons } from '../../../assets/icons/DashboardIcons';
import { ROUTES } from '../../../constants/routes';

interface MetricRowProps {
  metric: string;
  lastUpdated: string;
  trend: 'up' | 'down' | 'stable';
  value: string;
}

const MetricRow: React.FC<MetricRowProps> = ({
  metric,
  lastUpdated,
  trend,
  value,
}) => {
  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <Icons.TrendUp className="h-4 w-4" />;
      case 'down':
        return <Icons.TrendDown className="h-4 w-4" />;
      case 'stable':
        return <Icons.TrendStable className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <tr>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {metric}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {lastUpdated}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        <div className={`flex items-center`}>
          {getTrendIcon(trend)}
          <span className="ml-2">{value}</span>
        </div>
      </td>
    </tr>
  );
};

const IntelligenceSnapshot: React.FC = () => {
  const metrics = [
    {
      metric: 'Counteroffer Acceptance Rate',
      lastUpdated: '61%',
      trend: 'up' as const,
      value: '+3%',
    },
    {
      metric: 'Offers Proposed by Collin',
      lastUpdated: '61%',
      trend: 'up' as const,
      value: '+3%',
    },
    {
      metric: 'Offers Originated by Customers',
      lastUpdated: '61%',
      trend: 'stable' as const,
      value: 'Stable',
    },
    {
      metric: 'Offers via Scyra Outreach',
      lastUpdated: '61%',
      trend: 'down' as const,
      value: '-1%',
    },
    {
      metric: 'Average Approval Time',
      lastUpdated: '2.7 sec',
      trend: 'stable' as const,
      value: 'Fast',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Intelligence Snapshot (Analytics + Patterns)"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SETO_INTELLIGENCE_SNAPSHOT}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7]">
              <tr>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Metric
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Updated
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Trend
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {metrics.map((metric, index) => (
                <MetricRow
                  key={index}
                  metric={metric.metric}
                  lastUpdated={metric.lastUpdated}
                  trend={metric.trend}
                  value={metric.value}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default IntelligenceSnapshot;
