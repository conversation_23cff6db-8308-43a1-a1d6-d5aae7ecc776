import React from 'react';
import { Eye } from 'lucide-react';
import SectionTitle from '../../../components/common/SectionTitle';
import { Icons } from '../../../assets/icons/DashboardIcons';
import { ROUTES } from '../../../constants/routes';

interface DecisionRowProps {
  offerId: string;
  customerId: string;
  offerAmount: string;
  originalBalance: string;
  settlementPercent: string;
  decision: string;
  confidence: string;
  status: string;
}

const DecisionRow: React.FC<DecisionRowProps> = ({
  offerId,
  customerId,
  offerAmount,
  originalBalance,
  settlementPercent,
  decision,
  confidence,
  status,
}) => {
  const getActionButton = (decision: string) => {
    const baseClasses =
      'px-4 py-1.5 text-xs font-medium rounded-full text-blackOne flex items-center bg-transparent border border-blackOne ';

    switch (decision.toLowerCase()) {
      case 'accepted':
        return (
          <button className={`text-subText ${baseClasses} hover:bg-red-100`}>
            <Eye className="mr-1 h-3 w-3 text-red-500" />
            View
          </button>
        );
      case 'countered':
        return (
          <button className={`text-subText ${baseClasses} hover:bg-purple-200`}>
            <Icons.EditIcon className="mr-1 h-3 w-3" />
            Edit
          </button>
        );
      case 'rejected':
        return (
          <button className={`text-subText ${baseClasses} hover:bg-yellow-200`}>
            <Icons.BellNotification className="mr-1 h-3 w-3" />
            Flag
          </button>
        );
      default:
        return null;
    }
  };

  return (
    <tr>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {offerId}
      </td>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {offerAmount}
      </td>
      <td className="px-6 py-4 text-sm text-subText lg:text-[15px]">
        {originalBalance}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {settlementPercent}
      </td>
      <td className={`px-6 py-3 text-sm text-subText lg:text-[15px]`}>
        {decision}
      </td>
      <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">
        {confidence}
      </td>
      <td className={`px-6 py-3 text-sm text-subText lg:text-[15px]`}>
        {status}
      </td>
      <td className="px-6 py-3 text-sm lg:text-[15px]">
        {getActionButton(decision)}
      </td>
    </tr>
  );
};

const ActiveSettlementDecisions: React.FC = () => {
  const decisions = [
    {
      offerId: 'A1921',
      customerId: '#44329',
      offerAmount: '$420',
      originalBalance: '$1,200',
      settlementPercent: '35.0%',
      decision: 'Accepted',
      confidence: '94%',
      status: 'Sent via Scyra',
    },
    {
      offerId: 'B0083',
      customerId: '#84401',
      offerAmount: '$800',
      originalBalance: '$2,000',
      settlementPercent: '35.0%',
      decision: 'Countered',
      confidence: '81%',
      status: 'Awaiting reply',
    },
    {
      offerId: 'D1017',
      customerId: '#28331',
      offerAmount: '$300',
      originalBalance: '$5,500',
      settlementPercent: '35.0%',
      decision: 'Rejected',
      confidence: '96%',
      status: 'Escalated',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Active Settlement Decisions Table"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SETO_ACTIVE_SETTLEMENTS}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7]">
              <tr>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Offer ID
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Customer ID
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Offer Amount
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Original Balance
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Settlement %
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Decision
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Confidence
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Status
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              {decisions.map((decision, index) => (
                <DecisionRow
                  key={index}
                  offerId={decision.offerId}
                  customerId={decision.customerId}
                  offerAmount={decision.offerAmount}
                  originalBalance={decision.originalBalance}
                  settlementPercent={decision.settlementPercent}
                  decision={decision.decision}
                  confidence={decision.confidence}
                  status={decision.status}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ActiveSettlementDecisions;
