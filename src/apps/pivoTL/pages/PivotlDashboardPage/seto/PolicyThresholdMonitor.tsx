import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface PolicyRowProps {
  rule: string;
  value: string;
  editable: boolean;
  notes: string;
}

const PolicyRow: React.FC<PolicyRowProps> = ({
  rule,
  value,
  editable,
  notes,
}) => (
  <tr className="">
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{rule}</td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{value}</td>
    <td className="px-6 py-3 text-sm lg:text-[15px]">
      {editable ? (
        <span className="inline-flex items-center text-subText">Yes</span>
      ) : (
        <span className="inline-flex items-center text-subText">No</span>
      )}
    </td>
    <td className="px-6 py-3 text-sm text-subText lg:text-[15px]">{notes}</td>
  </tr>
);

const PolicyThresholdMonitor: React.FC = () => {
  const policies = [
    {
      rule: 'Minimum Acceptable Settlement',
      value: '18%',
      editable: true,
      notes: 'Below this = Auto-Reject',
    },
    {
      rule: 'Max Counter Discount Range',
      value: '20-35%',
      editable: true,
      notes: 'Seto uses this to auto-generate counteroffers',
    },
    {
      rule: 'Escalation Trigger Attempts',
      value: '3 consecutive rejections',
      editable: true,
      notes: 'Routes to human manager',
    },
    {
      rule: 'Real-Time Policy Violations Today',
      value: '12',
      editable: false,
      notes: 'Flagged for manual review',
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Policy Threshold Monitor (Live Rules)"
        className="mb-4"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SETO_POLICY_THRESHOLD}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-[#F4F5F7]">
              <tr>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Rule
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Value
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Editable
                </th>
                <th className="border-b border-[#F4F5F7] px-6 py-4 text-left text-xs font-medium text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {policies.map((policy, index) => (
                <PolicyRow
                  key={index}
                  rule={policy.rule}
                  value={policy.value}
                  editable={policy.editable}
                  notes={policy.notes}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PolicyThresholdMonitor;
