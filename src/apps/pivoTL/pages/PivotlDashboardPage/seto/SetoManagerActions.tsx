import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface ActionItemProps {
  action: string;
}

const ActionItem: React.FC<ActionItemProps> = ({ action }) => (
  <div className="flex items-center rounded-lg bg-white p-4">
    <div className="mr-4 flex h-[36px] w-[36px] items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
      <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
    </div>
    <span className="text-xs text-subText sm:text-sm lg:text-[15px]">
      {action}
    </span>
  </div>
);

const SetoManagerActions: React.FC = () => {
  const actions = [
    'Review 6 Offers Rejected Under 10% That May Qualify Under Hardship Exception',
    'Authorize A/B Testing for dynamic counter range of 22-38% on accounts <$1,000',
    'Consider raising minimum threshold for auto-approval from 18% to 20% based on current Q2 delinquency data',
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Suggested Manager Actions (Generated by Seto)"
        className="mb-4"
        showBrowseAll={false}
      />

      <div className="space-y-3">
        {actions.map((action, index) => (
          <ActionItem key={index} action={action} />
        ))}
      </div>
    </div>
  );
};

export default SetoManagerActions;
