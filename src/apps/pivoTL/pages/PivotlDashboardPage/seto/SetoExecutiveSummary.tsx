import React from 'react';
import MetricCard from '../../../components/common/MetricCard';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';
import SectionTitle from '@/apps/pivoTL/components/common/SectionTitle';

const SetoExecutiveSummary: React.FC = () => {
  const metrics = [
    {
      title: 'Offers Evaluated Today',
      value: '82',
      icon: <Icons.DataRecord className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Accepted',
      value: '41',
      icon: <Icons.CreditCardAccepted className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Countered',
      value: '22',
      icon: <Icons.AcceptAction className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Rejected',
      value: '9',
      icon: <Icons.CardRejected className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Average Settlement',
      value: '31.8%',
      icon: <Icons.ChartAverage className="h-4 w-4" />,
      trend: undefined,
    },
    {
      title: 'Projected Recovery(Today)',
      value: '$23,760',
      icon: <Icons.DataRecord className="h-4 w-4" />,
      trend: undefined,
    },
  ];

  return (
    <div className="mb-8">
      <SectionTitle
        title="Executive Summary"
        className="mb-4"
        showBrowseAll={false}
      />

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-6">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            trend={metric.trend}
          />
        ))}
      </div>
    </div>
  );
};

export default SetoExecutiveSummary;
