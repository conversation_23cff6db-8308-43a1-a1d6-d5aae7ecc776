import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Filter, Search } from 'lucide-react';
import { Button } from '@/components/ui/ButtonComponent/button';
import Pagination from '@/apps/pivoTL/components/common/Pagination';

interface CollectorData {
  rank: number;
  name: string;
  totalCollected: string;
  collectionRate: string;
  avgTimeToFirstContact: string;
  avgTimeToResolution: string;
  activeAccounts: number;
  completedAccounts: number;
  successRate: string;
  totalAccounts: number;
}

const CollectorPerformanceDetailPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const totalPages = 4;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading report...');
  };

  const collectorData: CollectorData[] = [
    {
      rank: 1,
      name: '<PERSON><PERSON>',
      totalCollected: '$321,000',
      collectionRate: '42%',
      avgTimeToFirstContact: '18 days',
      avgTimeToResolution: '4.1 days',
      activeAccounts: 8,
      completedAccounts: 12,
      successRate: '9.3%',
      totalAccounts: 119,
    },
    {
      rank: 2,
      name: 'Jane Morales',
      totalCollected: '$287,400',
      collectionRate: '37%',
      avgTimeToFirstContact: '22 days',
      avgTimeToResolution: '5.4 days',
      activeAccounts: 6,
      completedAccounts: 8,
      successRate: '12.1%',
      totalAccounts: 98,
    },
  ];

  const navigate = useNavigate();

  return (
    <div className="flex h-full flex-col space-y-6">
      {/* Header */}
      <div className="relative flex items-center justify-between gap-4">
        <div className="flex items-start gap-2">
          <button onClick={() => navigate(-1)} className="mt-1">
            <ChevronLeft className="h-5 w-5" strokeWidth={3} />
          </button>
          <div className="flex flex-col items-start gap-1 text-blackTwo">
            <h1 className="text-lg font-semibold">
              Collector Performance Table
            </h1>
            <p className="text-blackTwo0 text-sm">
              Rank collectors—including Scyra—across key performance indicators
              (KPIs) tied to business outcomes.
            </p>
          </div>
        </div>
        {/* Search and Filter */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
            <input
              type="text"
              placeholder="Search"
              className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
            />
          </div>
          <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
            <span>Filter</span>
            <Filter className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Table Content */}
      <div className="flex-1 overflow-hidden rounded-xl bg-white">
        <div className="h-[75vh] overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-white">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Rank
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Collector
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Total $ Recovered
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  % Value at Risk Recovered
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Time to Recovery
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Avg Stage Duration
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Settlements
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Rehabilitations
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Default Reoccurrence Rate
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                  Files Processed
                </th>
              </tr>
            </thead>
            <tbody className="">
              {collectorData.map(collector => (
                <tr key={collector.rank} className="hover:bg-gray-50">
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.rank}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-blackOne">
                        {collector.name}
                      </div>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.totalCollected}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.collectionRate}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.avgTimeToFirstContact}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.avgTimeToResolution}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.activeAccounts}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.completedAccounts}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.successRate}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-subText">
                    {collector.totalAccounts}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        onDownload={handleDownload}
        className="mt-auto"
        downloadButtonText="Download Report"
      />
    </div>
  );
};

export default CollectorPerformanceDetailPage;
