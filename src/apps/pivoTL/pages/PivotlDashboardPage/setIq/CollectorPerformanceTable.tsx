import { ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import React from 'react';
import { ROUTES } from '@/apps/pivoTL/constants/routes';

interface CollectorData {
  rank: number;
  name: string;
  totalCollected: string;
  collectionRate: string;
  avgTimeToFirstContact: string;
  avgTimeToResolution: string;
  activeAccounts: number;
  completedAccounts: number;
  successRate: string;
  totalAccounts: number;
}

const CollectorPerformanceTable: React.FC = () => {
  const navigate = useNavigate();
  const collectorData: CollectorData[] = [
    {
      rank: 1,
      name: '<PERSON>yra',
      totalCollected: '$321,000',
      collectionRate: '42%',
      avgTimeToFirstContact: '18 days',
      avgTimeToResolution: '4.1 days',
      activeAccounts: 8,
      completedAccounts: 12,
      successRate: '9.3%',
      totalAccounts: 119,
    },
    {
      rank: 2,
      name: '<PERSON>',
      totalCollected: '$287,400',
      collectionRate: '37%',
      avgTimeToFirstContact: '22 days',
      avgTimeToResolution: '5.4 days',
      activeAccounts: 6,
      completedAccounts: 8,
      successRate: '12.1%',
      totalAccounts: 98,
    },
  ];

  const handleBrowseAll = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETIQ_COLLECTOR_PERFORMANCE);
  };

  return (
    <div className="overflow-hidden rounded-xl bg-white">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-base font-medium text-blackOne">
              Collector Performance Table
            </h3>
            <p className="mt-1 text-sm text-blackTwo">
              Ranked collectors—including Scyra—across key performance
              indicators (KPIs) tied to business outcomes.
            </p>
          </div>
          <button
            className="flex items-center gap-2 border-b border-primary pb-2 text-sm font-normal text-primary"
            onClick={handleBrowseAll}
          >
            Browse all <ArrowRight className="h-4 w-4" />
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-white">
            <tr>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Rank
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Collector
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Total $ Recovered
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                % Value at Risk Recovered
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Time to Recovery
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Avg Stage Duration
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Settlements
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Rehabilitations
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Default Reoccurrence Rate
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium tracking-wider text-[#4F4F4F]">
                Files Processed
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {collectorData.map(collector => (
              <tr key={collector.rank} className="hover:bg-gray-50">
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.rank}
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-blackOne">
                      {collector.name}
                    </div>
                  </div>
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.totalCollected}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.collectionRate}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.avgTimeToFirstContact}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.avgTimeToResolution}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.activeAccounts}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.completedAccounts}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.successRate}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.totalAccounts}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CollectorPerformanceTable;
