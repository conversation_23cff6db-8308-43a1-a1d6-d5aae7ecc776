import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Filter, Search } from 'lucide-react';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';
import Button from '@/components/ui/ButtonComponent';
import Pagination from '@/apps/pivoTL/components/common/Pagination';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
  timestamp: string;
}

const InsightItem: React.FC<InsightData> = ({ insight, timestamp }) => {
  return (
    <div className="animate-fadeIn flex items-center justify-between rounded-lg bg-white p-4 transition-all duration-150 hover:border-primary">
      <div className="flex w-full items-center">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <div className="flex-1">
          <span className="mb-1 block text-xs font-normal text-subText sm:text-sm">
            {insight}
          </span>
          <span className="hidden text-xs text-gray-500">{timestamp}</span>
        </div>
      </div>
    </div>
  );
};

const DailyInsightsDetailPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const totalPages = 4;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading report...');
  };

  const insights: InsightData[] = [
    {
      id: '1',
      insight:
        'Yesterday, 3 newly created collections objects remained unassigned for over 6 hours—Obed may want to review assignment delay triggers.',
      type: 'warning',
      timestamp: '2 hours ago',
    },
    {
      id: '2',
      insight:
        'Authorize Collin to proceed with new negotiation sequence for 18 accounts',
      type: 'suggestion',
      timestamp: '4 hours ago',
    },
    {
      id: '3',
      insight:
        'Deploy alternate Recura strategy to test better timing for Gen Z segment',
      type: 'suggestion',
      timestamp: '6 hours ago',
    },
    {
      id: '4',
      insight:
        'Noticed improved collection rate in Retail Segment — 19.2% above average. Worth deeper review.',
      type: 'info',
      timestamp: '8 hours ago',
    },
    {
      id: '5',
      insight:
        "Jane Morales' average stage duration in 'Negotiation' rose to 71 days (team avg = 4.3).",
      type: 'warning',
      timestamp: '10 hours ago',
    },
  ];

  const navigate = useNavigate();

  return (
    <div className="flex h-full flex-col gap-6">
      {/* Header */}

      <div className="relative flex items-center justify-between gap-4">
        <div className="flex items-start gap-2">
          <button onClick={() => navigate(-1)} className="mt-1">
            <ChevronLeft className="h-5 w-5" strokeWidth={3} />
          </button>
          <div className="flex flex-col items-start gap-1 text-blackTwo">
            <h1 className="text-lg font-semibold">
              Daily Insight Commentary (From Scyra)
            </h1>
            <p className="text-blackTwo0 text-sm">
              AI-generated insights and recommendations based on daily
              performance patterns.
            </p>
          </div>
        </div>
        {/* Search and Filter */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
            <input
              type="text"
              placeholder="Search"
              className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
            />
          </div>
          <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
            <span>Filter</span>
            <Filter className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Insights Content */}
      <div className="flex flex-1 flex-col gap-4">
        {insights.map((insight, index) => (
          <InsightItem
            key={index}
            id={insight.id}
            insight={insight.insight}
            type={insight.type}
            timestamp={insight.timestamp}
          />
        ))}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        onDownload={handleDownload}
        className="mt-auto"
        downloadButtonText="Download Report"
      />
    </div>
  );
};

export default DailyInsightsDetailPage;
