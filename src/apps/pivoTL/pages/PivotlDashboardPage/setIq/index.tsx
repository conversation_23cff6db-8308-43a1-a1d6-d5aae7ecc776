import React, { useState, useRef, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import CollectorPerformanceTable from './CollectorPerformanceTable';
import DailyInsightCommentary from './DailyInsightCommentary';
import AgentSummaryTabs from './AgentSummaryTabs';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, Search, Filter } from 'lucide-react';
import Button from '@/components/ui/ButtonComponent';
import { scyraLogo } from '@/apps/pivoTL/assets/images';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import SectionTitle from '@/apps/pivoTL/components/common/SectionTitle';

interface DashboardOption {
  id: string;
  name: string;
  path: string;
  icon: string;
}

const ScyraDashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedDashboard, setSelectedDashboard] =
    useState<DashboardOption | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const dashboardOptions: DashboardOption[] = [
    {
      id: 'setiq',
      name: 'SetIQ Collections Intelligence Dashboard',
      path: ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD,
      icon: scyraLogo,
    },
  ];

  useEffect(() => {
    const currentPath = location.pathname;
    const currentDashboard =
      dashboardOptions.find(option => currentPath.includes(option.id)) ||
      dashboardOptions[1]; // Default to Seto
    setSelectedDashboard(currentDashboard);
  }, [location.pathname]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleDashboardSelect = (dashboard: DashboardOption) => {
    setSelectedDashboard(dashboard);
    setIsDropdownOpen(false);
    navigate(dashboard.path);
  };

  // Check if we're on a child route (not the main setiq dashboard)
  const isChildRoute =
    location.pathname !== ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD;

  return (
    <div className="flex h-full flex-col bg-gray-50">
      {/* Main Content */}
      <div className="flex-1 space-y-8 overflow-y-auto p-8">
        {/* Conditionally render main content or child routes */}
        {isChildRoute ? (
          <Outlet />
        ) : (
          <>
            {/* Header with Dropdown and Search/Filter - Only on parent page */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Custom Dropdown */}
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="flex h-[48px] items-center gap-3 rounded-lg border border-[#718EBF] bg-white px-4 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 md:w-fit"
                  >
                    <img
                      src={selectedDashboard?.icon || ''}
                      alt="Dashboard Icon"
                      className="h-6 w-6 sm:h-8 sm:w-8"
                    />
                    <span className="text-sm font-semibold text-blackOne">
                      {selectedDashboard?.name || 'SetIQ'}
                    </span>
                    {isDropdownOpen ? (
                      <ChevronUp className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </button>

                  {/* Dropdown Menu */}
                  {isDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute left-10 top-full z-50 mt-1 w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]"
                    >
                      <div className="py-1">
                        {dashboardOptions
                          .filter(option => option.id !== selectedDashboard?.id)
                          .map(option => (
                            <button
                              key={option.id}
                              onClick={() => handleDashboardSelect(option)}
                              className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm font-medium transition-colors hover:bg-gray-50 sm:text-base"
                            >
                              <img
                                src={option.icon}
                                alt="Dashboard Icon"
                                className="h-6 w-6 sm:h-8 sm:w-8"
                              />
                              {option.name}
                            </button>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>

              {/* Search and Filter */}
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
                  <input
                    type="text"
                    placeholder="Search"
                    className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
                  />
                </div>
                <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
                  <span>Filter</span>
                  <Filter className="h-5 w-5" />
                </Button>
              </div>
            </div>

            <div className="space-y-8">
              {/* Agent Performance Comparison Table */}
              <div className="space-y-4">
                <h2 className="font-medium text-blackOne sm:text-lg">
                  Agent Performance Comparison Table
                </h2>
                <CollectorPerformanceTable />
              </div>

              {/* Daily Insight Commentary */}
              <div className="overflow-hidden rounded-xl bg-white p-4">
                <SectionTitle
                  title="Daily Insight Commentary (From Scyra) (3 Insights)"
                  className="mb-4"
                  browseAllRoute={
                    ROUTES.DASHBOARD_ANALYTICS_SETIQ_DAILY_INSIGHTS
                  }
                />
                <DailyInsightCommentary />
              </div>

              {/* Agent Summary */}
              <AgentSummaryTabs />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ScyraDashboard;
