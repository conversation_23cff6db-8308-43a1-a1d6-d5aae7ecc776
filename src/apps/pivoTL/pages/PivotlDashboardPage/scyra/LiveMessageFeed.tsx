import React, { useState } from 'react';
import { Eye, Bell, Check } from 'lucide-react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface MessageRowProps {
  customerId: string;
  lastMessageFrom: string;
  tone: string;
  status: string;
  action: {
    type: 'view' | 'notify' | 'confirm';
    label: string;
  };
}

const MessageRow: React.FC<MessageRowProps> = ({
  customerId,
  lastMessageFrom,
  tone,
  status,
  action,
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'escalated':
        return 'text-red-600';
      case 'commitment sent':
        return 'text-green-600';
      case 'awaiting reply':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getActionButton = (action: { type: string; label: string }) => {
    const baseClasses =
      'px-4 py-1.5 text-xs font-medium rounded-full text-blackOne flex items-center bg-transparent border border-blackOne ';

    switch (action.type) {
      case 'view':
        return (
          <button className={`${baseClasses} hover:bg-blue-200`}>
            <Eye className="mr-1 h-3 w-3 text-[#FF3E00]" />
            {action.label}
          </button>
        );
      case 'notify':
        return (
          <button className={`${baseClasses} hover:bg-yellow-200`}>
            <Bell className="mr-1 h-3 w-3 text-[#4A35CB]" />
            {action.label}
          </button>
        );
      case 'confirm':
        return (
          <button
            className={`${baseClasses} hover:bg-green-200 hover:border-green-700`}
          >
            <Check className="mr-1 h-3 w-3 text-[#23BD33]" />
            {action.label}
          </button>
        );
      default:
        return null;
    }
  };

  return (
    <tr>
      <td className="px-6 py-1.5 text-xs text-subText sm:text-sm lg:text-[15px]">
        {customerId}
      </td>
      <td className="px-6 py-1.5 text-xs text-subText sm:text-sm lg:text-[15px]">
        {lastMessageFrom}
      </td>
      <td className="px-6 py-1.5 text-xs text-subText sm:text-sm lg:text-[15px]">
        {tone}
      </td>
      <td
        className={`px-6 py-1.5 text-xs text-subText sm:text-sm lg:text-[15px] ${getStatusColor(status)}`}
      >
        {status}
      </td>
      <td className="px-6 py-1.5 text-xs sm:text-sm lg:text-[15px]">
        {getActionButton(action)}
      </td>
    </tr>
  );
};

const LiveMessageFeed: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');

  const tabs = [
    { id: 'all', label: 'All' },
    { id: 'awaiting', label: 'Awaiting Reply' },
    { id: 'commitment', label: 'Commitment Pending' },
    { id: 'negative', label: 'Negative Sentiment' },
    { id: 'escalated', label: 'Escalated' },
  ];

  const messages = [
    {
      customerId: '#39029',
      lastMessageFrom: 'Scyra',
      tone: 'Neutral',
      status: 'Awaiting reply',
      action: { type: 'view' as const, label: 'View' },
    },
    {
      customerId: '#84401',
      lastMessageFrom: 'Customer',
      tone: 'Negative',
      status: 'Escalated',
      action: { type: 'notify' as const, label: 'Notify' },
    },
    {
      customerId: '#28331',
      lastMessageFrom: 'Customer',
      tone: 'Positive',
      status: 'Commitment Sent',
      action: { type: 'confirm' as const, label: 'Confirm' },
    },
  ];

  return (
    <div className="max-w-[1000px] space-y-4">
      <SectionTitle
        title="Live Message Feed"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SCYRA_LIVE_MESSAGE_FEED}
      />

      {/* Tabs */}
      <nav className="-mb-px flex space-x-8 border-b">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`border-b-2 px-1 py-2 text-sm font-medium ${
              activeTab === tab.id
                ? 'border-primary'
                : 'border-transparent font-light text-[#1D2026] hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </nav>

      {/* Messages Table */}
      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Customer ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Message From
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Tone (via Senti)
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {messages.map((message, index) => (
                <MessageRow
                  key={index}
                  customerId={message.customerId}
                  lastMessageFrom={message.lastMessageFrom}
                  tone={message.tone}
                  status={message.status}
                  action={message.action}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default LiveMessageFeed;
