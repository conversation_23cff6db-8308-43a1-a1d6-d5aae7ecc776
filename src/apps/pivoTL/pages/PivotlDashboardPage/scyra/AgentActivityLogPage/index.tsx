import React, { useState } from 'react';
import { ChevronLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

interface ActivityRowProps {
  time: string;
  lastMessageFrom: string;
  action: string;
}

const ActivityRow: React.FC<ActivityRowProps> = ({
  time,
  lastMessageFrom,
  action,
}) => (
  <tr className="transition-colors duration-150 hover:bg-gray-50">
    <td className="f px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {time}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {lastMessageFrom}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {action}
    </td>
  </tr>
);

const AgentActivityLogPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'SMS Actions', value: 'sms' },
    { id: '2', label: 'Email Actions', value: 'email' },
    { id: '3', label: 'Escalations', value: 'escalation' },
    { id: '4', label: 'Follow-ups', value: 'followup' },
    { id: '5', label: 'Routing Actions', value: 'routing' },
    { id: '6', label: 'Sentiment Detection', value: 'sentiment' },
  ];

  const activities = [
    {
      time: '9:42 AM',
      lastMessageFrom: 'Sent SMS to #39029',
      action: 'No response in 48h since last follow-up',
    },
    {
      time: '10:13 AM',
      lastMessageFrom: 'Paused follow-up to #84401',
      action: 'Detected negative sentiment via Observe.AI',
    },
    {
      time: '11:05 AM',
      lastMessageFrom: 'Routed #28331 to Collin',
      action: 'Customer expressed willingness to pay',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SCYRA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    // Handle download logic here
    console.log('Downloading report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="flex h-full flex-col gap-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col items-start gap-2 text-blackOne">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              Agent Activity Log (Trail Integration)
            </button>{' '}
            <p className="text-sm sm:text-base">
              See what Scyra did, when, and why. Clickable for full context.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Icons.Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-10 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn h-full max-w-[1000px] flex-1 overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Time
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Last Message From
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {activities.map((activity, index) => (
                  <ActivityRow
                    key={index}
                    time={activity.time}
                    lastMessageFrom={activity.lastMessageFrom}
                    action={activity.action}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div className="mt-auto">
          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            onDownload={handleDownload}
            downloadButtonText="Download Report"
          />
        </div>
      </AppContainer>
    </div>
  );
};

export default AgentActivityLogPage;
