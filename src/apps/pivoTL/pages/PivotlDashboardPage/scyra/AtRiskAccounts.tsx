import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface RiskRowProps {
  customerId: string;
  riskScore: string;
  lastStrategy: string;
  nextRecommendation: string;
}

const RiskRow: React.FC<RiskRowProps> = ({
  customerId,
  riskScore,
  lastStrategy,
  nextRecommendation,
}) => {
  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      // case 'high':
      //   return 'text-red-600';
      // case 'medium':
      //   return 'text-yellow-600';
      // case 'low':
      //   return 'text-green-600';
      default:
        return 'text-subText';
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        {customerId}
      </td>
      <td
        className={`px-6 py-3 text-xs font-normal sm:text-sm ${getRiskColor(riskScore)}`}
      >
        {riskScore}
      </td>
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        {lastStrategy}
      </td>
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        {nextRecommendation}
      </td>
    </tr>
  );
};

const AtRiskAccounts: React.FC = () => {
  const atRiskAccounts = [
    {
      customerId: '#39029',
      riskScore: 'High',
      lastStrategy: 'Email+SMS Combo',
      nextRecommendation: 'Switch to human agent',
    },
    {
      customerId: '#84401',
      riskScore: 'Medium',
      lastStrategy: 'Daily Text',
      nextRecommendation: 'Pause for 3 days',
    },
    {
      customerId: '#28331',
      riskScore: 'High',
      lastStrategy: '3 failed follow-ups',
      nextRecommendation: 'Escalate to Collin',
    },
  ];

  return (
    <div className="max-w-[1000px] space-y-4">
      <div className="mb-6">
        <SectionTitle
          title="At-Risk Accounts (With Recura Flags)"
          browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SCYRA_AT_RISK_ACCOUNTS}
          className="mb-1"
        />
        <p className="text-sm text-subText lg:text-[15px]">
          Prioritized list based on strategy failure or customer frustration.
        </p>
      </div>

      <div className="animate-fadeIn overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Customer ID
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Risk Score
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Strategy
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Next Recommendation
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {atRiskAccounts.map((account, index) => (
                <RiskRow
                  key={index}
                  customerId={account.customerId}
                  riskScore={account.riskScore}
                  lastStrategy={account.lastStrategy}
                  nextRecommendation={account.nextRecommendation}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AtRiskAccounts;
