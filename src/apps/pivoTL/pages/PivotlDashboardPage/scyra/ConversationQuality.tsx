import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface MetricRowProps {
  metric: string;
  today: string;
  trend: string;
  notes: string;
}

const MetricRow: React.FC<MetricRowProps> = ({
  metric,
  today,
  trend,
  notes,
}) => (
  <tr>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {metric}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {today}
    </td>
    <td
      className={`px-6 py-3 text-xs font-normal sm:text-sm lg:text-[15px] ${
        trend.startsWith('+')
          ? 'text-green-600'
          : trend.startsWith('-')
            ? 'text-red-600'
            : 'text-[#718EBF]'
      }`}
    >
      {trend}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px] ">
      {notes}
    </td>
  </tr>
);

const ConversationQuality: React.FC = () => {
  const metrics = [
    {
      metric: 'Open Rate',
      today: '73%',
      trend: '+4%',
      notes: 'Compared to prior week',
    },
    {
      metric: 'Response Rate',
      today: '58%',
      trend: '+2%',
      notes: '-',
    },
    {
      metric: 'Positive Sentiment (from Senti)',
      today: '41%',
      trend: '-1%',
      notes: 'Compared to prior week',
    },
    {
      metric: 'Commitments Confirmed',
      today: '36',
      trend: '-',
      notes: 'Collin takes over here',
    },
    {
      metric: 'Escalations Triggered',
      today: '8',
      trend: '-',
      notes: 'Routed to human agents or supervisor',
    },
    {
      metric: 'Avg. Response Time (Agent)',
      today: '2.4 min',
      trend: '-',
      notes: 'Human escalation response time',
    },
  ];

  return (
    <div className="max-w-[1000px] space-y-4">
      <SectionTitle
        title="Conversation Quality & Outcomes (Main Panel)"
        browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SCYRA_CONVERSATION_QUALITY}
      />

      <div className="overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Metric
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Today
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  7-Day Trend
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody className="mb-4 bg-white">
              {metrics.map((metric, index) => (
                <MetricRow
                  key={index}
                  metric={metric.metric}
                  today={metric.today}
                  trend={metric.trend}
                  notes={metric.notes}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ConversationQuality;
