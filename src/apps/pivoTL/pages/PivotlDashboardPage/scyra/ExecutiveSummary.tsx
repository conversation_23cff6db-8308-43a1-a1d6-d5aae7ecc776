import React from 'react';
import MetricCard from '../../../components/common/MetricCard';
import SectionTitle from '../../../components/common/SectionTitle';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';

const ExecutiveSummary: React.FC = () => {
  const metrics = [
    {
      title: 'Total Contacts Engaged Today',
      value: '528',
      icon: <Icons.User className="h-4 w-4 text-primary" />,
      trend: '+12%',
    },
    {
      title: 'Active Conversations',
      value: '104',
      icon: <Icons.Chat className="h-4 w-4 text-primary" />,
      trend: '+5%',
    },
    {
      title: 'Commitments Generated',
      value: '36',
      icon: <Icons.DocumentStack className="h-4 w-4 text-primary" />,
      trend: '+8%',
    },
    {
      title: 'At-Risk Cases',
      value: '12',
      icon: <Icons.RiskAnalysis className="h-4 w-4 text-primary" />,
      trend: '-3%',
    },
    {
      title: 'Follow-Ups \n' + 'Scheduled (Next 24h)',
      value: '87',
      icon: <Icons.Loop className="h-4 w-4 text-primary" />,
      trend: '+15%',
    },
  ];

  return (
    <div className="space-y-4">
      <SectionTitle title="Executive Summary" showBrowseAll={false} />

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            trend={metric.trend}
          />
        ))}
      </div>
    </div>
  );
};

export default ExecutiveSummary;
