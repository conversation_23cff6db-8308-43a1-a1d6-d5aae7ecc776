import React from 'react';
import SectionTitle from '../../../components/common/SectionTitle';
import { ROUTES } from '../../../constants/routes';

interface ActivityRowProps {
  time: string;
  lastMessageFrom: string;
  action: string;
}

const ActivityRow: React.FC<ActivityRowProps> = ({
  time,
  lastMessageFrom,
  action,
}) => (
  <tr className="duration-15 transition-colors">
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {time}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {lastMessageFrom}
    </td>
    <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm lg:text-[15px]">
      {action}
    </td>
  </tr>
);

const AgentActivityLog: React.FC = () => {
  const activities = [
    {
      time: '9:42 AM',
      lastMessageFrom: 'Sent SMS to #39029',
      action: 'No response in 48h since last follow-up',
    },
    {
      time: '10:13 AM',
      lastMessageFrom: 'Paused follow-up to #84401',
      action: 'Detected negative sentiment via Observe.AI',
    },
    {
      time: '11:05 AM',
      lastMessageFrom: 'Routed #28331 to Collin',
      action: 'Customer expressed willingness to pay',
    },
  ];

  return (
    <div className="max-w-[1000px] space-y-4">
      <div className="mb-6">
        <SectionTitle
          title="Agent Activity Log (Trail Integration)"
          browseAllRoute={ROUTES.DASHBOARD_ANALYTICS_SCYRA_AGENT_ACTIVITY_LOG}
          className="mb-1"
        />
        <p className="text-sm text-gray-600">
          See what Scyra did, when, and why. Clickable for full context.
        </p>
      </div>

      <div className="animate-fadeIn overflow-hidden rounded-lg bg-white">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Time
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Last Message From
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm lg:text-[15px]">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {activities.map((activity, index) => (
                <ActivityRow
                  key={index}
                  time={activity.time}
                  lastMessageFrom={activity.lastMessageFrom}
                  action={activity.action}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AgentActivityLog;
