import React, { useState } from 'react';
import { ChevronLeft, Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ActionDropdown from '@/apps/pivoTL/components/common/ActionDropdown';
import Pagination from '@/apps/pivoTL/components/common/Pagination';
import CustomDropdown from '@/apps/pivoTL/components/common/CustomDropdown';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import AppContainer from '@/apps/pivoTL/components/common/AppContainer';

interface RiskRowProps {
  customerId: string;
  riskScore: string;
  lastStrategy: string;
  nextRecommendation: string;
}

const RiskRow: React.FC<RiskRowProps> = ({
  customerId,
  riskScore,
  lastStrategy,
  nextRecommendation,
}) => {
  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <tr className="transition-colors duration-150 hover:bg-gray-50">
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        {customerId}
      </td>
      <td
        className={`px-6 py-3 text-xs font-normal sm:text-sm ${getRiskColor(riskScore)}`}
      >
        {riskScore}
      </td>
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        {lastStrategy}
      </td>
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        {nextRecommendation}
      </td>
      <td className="px-6 py-3 text-xs font-normal text-subText sm:text-sm">
        <ActionDropdown />
      </td>
    </tr>
  );
};

const AtRiskAccountsPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const totalPages = 4;

  const filterOptions = [
    { id: '1', label: 'High Risk', value: 'high' },
    { id: '2', label: 'Medium Risk', value: 'medium' },
    { id: '3', label: 'Low Risk', value: 'low' },
    { id: '4', label: 'Email Strategy', value: 'email' },
    { id: '5', label: 'SMS Strategy', value: 'sms' },
    { id: '6', label: 'Human Escalation', value: 'human' },
  ];

  const atRiskAccounts = [
    {
      customerId: '#39029',
      riskScore: 'High',
      lastStrategy: 'Email+SMS Combo',
      nextRecommendation: 'Switch to human agent',
    },
    {
      customerId: '#84401',
      riskScore: 'Medium',
      lastStrategy: 'Daily Text',
      nextRecommendation: 'Pause for 3 days',
    },
    {
      customerId: '#28331',
      riskScore: 'High',
      lastStrategy: '3 failed follow-ups',
      nextRecommendation: 'Escalate to Collin',
    },
  ];

  const handleGoBack = () => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_SCYRA);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    // Handle download logic here
    console.log('Downloading report...');
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
  };

  return (
    <div className="min-h-screen">
      <AppContainer className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col items-start gap-2 text-blackOne">
            <button
              onClick={handleGoBack}
              className="flex items-center font-semibold"
            >
              <ChevronLeft className="mr-2 h-5 w-5" />
              At-Risk Accounts (With Recura Flags)
            </button>
            <p className="text-sm sm:text-base">
              Prioritized list based on strategy failure or customer
              frustration.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
              <input
                type="text"
                placeholder="Search"
                className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
              />
            </div>
            <CustomDropdown
              options={filterOptions}
              selectedValues={selectedFilters}
              onSelectionChange={handleFilterChange}
              buttonText="Filter"
            />
          </div>
        </div>

        {/* Table */}
        <div className="animate-fadeIn h-full flex-1 overflow-hidden rounded-xl bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm">
                    Customer ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm">
                    Risk Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm">
                    Last Strategy
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm">
                    Next Recommendation
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-[#718EBF] sm:text-sm">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {atRiskAccounts.map((account, index) => (
                  <RiskRow
                    key={index}
                    customerId={account.customerId}
                    riskScore={account.riskScore}
                    lastStrategy={account.lastStrategy}
                    nextRecommendation={account.nextRecommendation}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onDownload={handleDownload}
          downloadButtonText="Download Report"
        />
      </AppContainer>
    </div>
  );
};

export default AtRiskAccountsPage;
