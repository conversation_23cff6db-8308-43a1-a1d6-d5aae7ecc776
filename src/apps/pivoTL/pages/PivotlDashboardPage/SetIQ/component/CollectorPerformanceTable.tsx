import React from 'react';

interface CollectorData {
  rank: number;
  name: string;
  totalCollected: string;
  collectionRate: string;
  avgTimeToFirstContact: string;
  avgTimeToResolution: string;
  activeAccounts: number;
  completedAccounts: number;
  successRate: string;
  totalAccounts: number;
}

const CollectorPerformanceTable: React.FC = () => {
  const collectorData: CollectorData[] = [
    {
      rank: 1,
      name: '<PERSON><PERSON>',
      totalCollected: '$321,000',
      collectionRate: '42%',
      avgTimeToFirstContact: '18 days',
      avgTimeToResolution: '4.1 days',
      activeAccounts: 8,
      completedAccounts: 12,
      successRate: '9.3%',
      totalAccounts: 119,
    },
    {
      rank: 2,
      name: '<PERSON>',
      totalCollected: '$287,400',
      collectionRate: '37%',
      avgTimeToFirstContact: '22 days',
      avgTimeToResolution: '5.4 days',
      activeAccounts: 6,
      completedAccounts: 8,
      successRate: '12.1%',
      totalAccounts: 98,
    },
  ];

  return (
    <div className="overflow-hidden rounded-xl bg-white">
      <div className="px-6 py-4">
        <h3 className="text-lg font-medium text-blackOne">
          Collector Performance Table
        </h3>
        <p className="mt-1 text-sm text-gray-600">
          Ranked collectors—including Scyra—across key performance indicators
          (KPIs) tied to business outcomes.
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <tbody className="bg-white">
            {collectorData.map(collector => (
              <tr key={collector.rank} className="hover:bg-gray-50">
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.rank}
                </td>
                <td className="whitespace-nowrap px-6 py-4">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-blackOne">
                      {collector.name}
                    </div>
                  </div>
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.totalCollected}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.collectionRate}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.avgTimeToFirstContact}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.avgTimeToResolution}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.activeAccounts}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.completedAccounts}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.successRate}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                  {collector.totalAccounts}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CollectorPerformanceTable;
