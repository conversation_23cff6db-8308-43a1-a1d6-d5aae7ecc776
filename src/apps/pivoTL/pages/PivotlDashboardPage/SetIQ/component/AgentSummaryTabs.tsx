import React, { useState } from 'react';

type SummaryTab = 'Obed Summary' | 'Colton Summary';

interface SummaryData {
  objectsCreated: number;
  errorRate: string;
  timeToCreation: string;
  assignedWithinTwoHours?: number;
  optimalCollectorRate?: string;
  filesReassigned?: string;
}

const AgentSummaryTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SummaryTab>('Obed Summary');

  const tabs: SummaryTab[] = ['Obed Summary', 'Colton Summary'];

  const summaryData: Record<SummaryTab, SummaryData> = {
    'Obed Summary': {
      objectsCreated: 141,
      errorRate: '1.4%',
      timeToCreation: '2.1 hrs',
      assignedWithinTwoHours: 141,
      optimalCollectorRate: '1.4%',
      filesReassigned: '17 cases',
    },
    'Colton Summary': {
      objectsCreated: 91,
      errorRate: '6.3%',
      timeToCreation: '5.6 hrs',
      assignedWithinTwoHours: 91,
      optimalCollectorRate: '1.4%',
      filesReassigned: '8 cases',
    },
  };

  return (
    <div className="w-full rounded-xl bg-white">
      {/* Tabs */}
      <div className="mb-4 flex space-x-1 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`p-4 text-sm font-medium transition-colors ${
              activeTab === tab
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-600 hover:text-blackOne'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Metrics Table */}
      <div className="max-w-[700px] p-6">
        <div className="space-y-4">
          {/* Objects Created Row */}
          <div className="flex items-center justify-between py-3">
            <span className="text-sm font-medium text-gray-900">
              Objects Created (Last 60 Days)
            </span>
            <span className="w-12 text-right text-sm text-gray-900">
              {summaryData['Obed Summary'].objectsCreated}
            </span>
            <span className="w-12 text-right text-sm text-gray-900">
              {summaryData['Colton Summary'].objectsCreated}
            </span>
          </div>

          {/* Error Rate Row */}
          <div className="flex items-center justify-between py-3">
            <span className="text-sm font-medium text-gray-900">
              Error Rate (Duplicate or Mislinked)
            </span>

            <span className="w-12 text-right text-sm text-gray-900">
              {summaryData['Obed Summary'].errorRate}
            </span>
            <span className="w-12 text-right text-sm text-gray-900">
              {summaryData['Colton Summary'].errorRate}
            </span>
          </div>

          {/* Time to Creation Row */}
          <div className="flex items-center justify-between py-3">
            <span className="text-sm font-medium text-gray-900">
              Time to Object Creation Post Bounce
            </span>
            <span className="w-12 text-right text-sm text-gray-900">
              {summaryData['Obed Summary'].timeToCreation}
            </span>
            <span className="w-12 text-right text-sm text-gray-900">
              {summaryData['Colton Summary'].timeToCreation}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentSummaryTabs;
