import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';
import React from 'react';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
}

const InsightItem: React.FC<InsightData> = ({ insight }) => {
  return (
    <div
      className={`animate-fadeIn flex items-center justify-between rounded-lg bg-white p-4 transition-all duration-150 hover:border-primary`}
    >
      <div className="flex items-center">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <span className="text-xs font-normal text-subText sm:text-sm">
          {insight}
        </span>
      </div>
    </div>
  );
};

const DailyInsightCommentary: React.FC = () => {
  const insights: InsightData[] = [
    {
      id: '1',
      insight:
        'Yesterday, 3 newly created collections objects remained unassigned for over 6 hours—<PERSON><PERSON> may want to review assignment delay triggers.',
      type: 'warning',
    },
    {
      id: '2',
      insight:
        'Noticed improved collection rate in Retail Segment — 19.2% above average. Worth deeper review.',
      type: 'info',
    },
    {
      id: '3',
      insight:
        "Jane Morales' average stage duration in 'Negotiation' rose to 71 days (team avg = 4.3).",
      type: 'suggestion',
    },
  ];

  return (
    <div className="flex flex-col divide-y divide-gray-100">
      {insights.map((insight, index) => (
        <InsightItem
          key={index}
          id={insight.id}
          insight={insight.insight}
          type={insight.type}
        />
      ))}
    </div>
  );
};

export default DailyInsightCommentary;
