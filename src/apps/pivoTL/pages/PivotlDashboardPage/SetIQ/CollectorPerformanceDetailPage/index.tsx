import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';

interface CollectorData {
  rank: number;
  name: string;
  totalCollected: string;
  collectionRate: string;
  avgTimeToFirstContact: string;
  avgTimeToResolution: string;
  activeAccounts: number;
  completedAccounts: number;
  successRate: string;
  totalAccounts: number;
}

const CollectorPerformanceDetailPage: React.FC = () => {
  const collectorData: CollectorData[] = [
    {
      rank: 1,
      name: '<PERSON><PERSON>',
      totalCollected: '$321,000',
      collectionRate: '42%',
      avgTimeToFirstContact: '18 days',
      avgTimeToResolution: '4.1 days',
      activeAccounts: 8,
      completedAccounts: 12,
      successRate: '9.3%',
      totalAccounts: 119,
    },
    {
      rank: 2,
      name: '<PERSON>',
      totalCollected: '$287,400',
      collectionRate: '37%',
      avgTimeToFirstContact: '22 days',
      avgTimeToResolution: '5.4 days',
      activeAccounts: 6,
      completedAccounts: 8,
      successRate: '12.1%',
      totalAccounts: 98,
    },
  ];

  return (
    <div className="flex h-full flex-col bg-gray-50">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link
              to="/pivotl/dashboard/analytics/scyra"
              className="flex items-center gap-2 text-gray-600 hover:text-blackOne"
            >
              <ChevronLeft className="h-4 w-4" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-blackOne">
                Collector Performance Table
              </h1>
              <p className="text-sm text-gray-600">
                Rank collectors—including Scyra—across key performance
                indicators (KPIs) tied to business outcomes.
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                className="w-64 rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              />
              <svg
                className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <button className="flex items-center gap-2 rounded-lg border border-primary px-4 py-2 text-sm font-medium text-primary hover:bg-orange-50">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
              </svg>
              Filter
            </button>
          </div>
        </div>
      </div>

      {/* Table Content */}
      <div className="flex-1 overflow-y-auto p-8">
        <div className="overflow-hidden rounded-lg bg-white shadow-sm">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Collector
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Collected
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Collection Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Avg Time to First Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Avg Time to Resolution
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Active Accounts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Completed Accounts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Success Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Accounts
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {collectorData.map(collector => (
                  <tr key={collector.rank} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.rank}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-blackOne">
                          {collector.name}
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.totalCollected}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.collectionRate}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.avgTimeToFirstContact}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.avgTimeToResolution}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.activeAccounts}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.completedAccounts}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.successRate}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-blackOne">
                      {collector.totalAccounts}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Download and Pagination */}
        <div className="mt-6 flex items-center justify-between">
          <button className="flex items-center gap-2 rounded-lg bg-gray-700 px-4 py-2 text-sm font-medium text-white hover:bg-gray-800">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7,10 12,15 17,10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            Download Report
          </button>

          <div className="flex items-center gap-2">
            <button className="flex items-center gap-1 text-gray-600 hover:text-blackOne">
              <ChevronLeft className="h-4 w-4" />
              Previous
            </button>
            <div className="flex gap-1">
              <button className="h-8 w-8 rounded bg-primary text-sm font-medium text-white">
                1
              </button>
              <button className="h-8 w-8 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                2
              </button>
              <button className="h-8 w-8 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                3
              </button>
              <button className="h-8 w-8 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                4
              </button>
            </div>
            <button className="flex items-center gap-1 text-gray-600 hover:text-blackOne">
              Next
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M5 12h14M12 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollectorPerformanceDetailPage;
