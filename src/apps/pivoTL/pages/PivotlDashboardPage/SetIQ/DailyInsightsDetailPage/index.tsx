import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { Icons } from '@/apps/pivoTL/assets/icons/DashboardIcons';
import { ROUTES } from '@/apps/pivoTL/constants/routes';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
  timestamp: string;
}

const InsightItem: React.FC<InsightData> = ({ insight, timestamp }) => {
  return (
    <div className="animate-fadeIn mb-4 flex items-center justify-between rounded-lg bg-white p-4 transition-all duration-150 hover:border-primary">
      <div className="flex w-full items-center">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <div className="flex-1">
          <span className="mb-1 block text-xs font-normal text-subText sm:text-sm">
            {insight}
          </span>
          <span className="text-xs text-gray-500">{timestamp}</span>
        </div>
      </div>
    </div>
  );
};

const DailyInsightsDetailPage: React.FC = () => {
  const insights: InsightData[] = [
    {
      id: '1',
      insight:
        'Yesterday, 3 newly created collections objects remained unassigned for over 6 hours—Obed may want to review assignment delay triggers.',
      type: 'warning',
      timestamp: '2 hours ago',
    },
    {
      id: '2',
      insight:
        'Authorize Collin to proceed with new negotiation sequence for 18 accounts',
      type: 'suggestion',
      timestamp: '4 hours ago',
    },
    {
      id: '3',
      insight:
        'Deploy alternate Recura strategy to test better timing for Gen Z segment',
      type: 'suggestion',
      timestamp: '6 hours ago',
    },
    {
      id: '4',
      insight:
        'Noticed improved collection rate in Retail Segment — 19.2% above average. Worth deeper review.',
      type: 'info',
      timestamp: '8 hours ago',
    },
    {
      id: '5',
      insight:
        "Jane Morales' average stage duration in 'Negotiation' rose to 71 days (team avg = 4.3).",
      type: 'warning',
      timestamp: '10 hours ago',
    },
  ];

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link
              to={ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD}
              className="flex items-center gap-2 text-gray-600 hover:text-blackOne"
            >
              <ChevronLeft className="h-4 w-4" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-blackOne">
                Daily Insight Commentary (From Scyra)
              </h1>
              <p className="text-sm text-gray-600">
                AI-generated insights and recommendations based on daily
                performance patterns.
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                className="w-64 rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              />
              <svg
                className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <button className="flex items-center gap-2 rounded-lg border border-primary px-4 py-2 text-sm font-medium text-primary hover:bg-orange-50">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
              </svg>
              Filter
            </button>
          </div>
        </div>
      </div>

      {/* Insights Content */}
      <div className="flex-1 overflow-y-auto p-8">
        <div className="flex flex-col divide-y divide-gray-100">
          {insights.map((insight, index) => (
            <InsightItem
              key={index}
              id={insight.id}
              insight={insight.insight}
              type={insight.type}
              timestamp={insight.timestamp}
            />
          ))}
        </div>

        {/* Download and Pagination */}
        <div className="mt-6 flex items-center justify-between">
          <button className="flex items-center gap-2 rounded-lg bg-gray-700 px-4 py-2 text-sm font-medium text-white hover:bg-gray-800">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7,10 12,15 17,10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            Download Report
          </button>

          <div className="flex items-center gap-2">
            <button className="flex items-center gap-1 text-gray-600 hover:text-blackOne">
              <ChevronLeft className="h-4 w-4" />
              Previous
            </button>
            <div className="flex gap-1">
              <button className="h-8 w-8 rounded bg-primary text-sm font-medium text-white">
                1
              </button>
              <button className="h-8 w-8 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                2
              </button>
              <button className="h-8 w-8 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                3
              </button>
              <button className="h-8 w-8 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                4
              </button>
            </div>
            <button className="flex items-center gap-1 text-gray-600 hover:text-blackOne">
              Next
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M5 12h14M12 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DailyInsightsDetailPage;
