import React, { useState } from 'react';
import { agentSuites, marketplaceAgents } from '../../data/constants';
import { AgentCard } from '../PivotlAiAgentsPage';
import { ROUTES } from '../../constants/routes';
import clsx from 'clsx';
import { dashboardIcons } from '../../assets/images';

type DashboardTab = 'Agent Suites' | 'Agentic AI';

interface DashboardWelcomeProps {
  onServiceSelect?: () => void;
}

const DashboardWelcome: React.FC<DashboardWelcomeProps> = () => {
  const [activeTab, setActiveTab] = useState<DashboardTab>('Agent Suites');

  const tabs: DashboardTab[] = ['Agent Suites', 'Agentic AI'];

  return (
    <div className="flex h-full flex-col gap-8 bg-gray-50 p-8">
      {/* Header */}
      <div className="text-start">
        <h1 className="mb-2 text-2xl font-semibold text-blackOne">Dashboard</h1>

        {/* Hero Section */}
        <div className="mx-auto flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-[#040721] text-white">
          <div className="flex w-full items-center justify-between">
            <div className="p-6 text-left">
              <h2 className="mb-2 text-lg font-bold">
                Actionable Intelligence across all Agentic AI agents.
              </h2>
              <p className="text-gray-300">
                Compare performance, monitor activity, and act on daily
                insights.
              </p>
            </div>
            <div className="relative mr-8 h-full w-[158px]">
              <img src={dashboardIcons} alt="bg" className="h-full w-full" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={clsx(
              'px-4 py-2 text-sm font-medium transition-colors',
              activeTab === tab
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-600 hover:text-blackOne',
            )}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="w-fit">
        {activeTab === 'Agent Suites' && (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
            {agentSuites.map(suite => (
              <AgentCard
                key={suite.id}
                agent={suite}
                link={ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD}
                className="flex h-[195px] w-full max-w-[334px] items-center bg-white"
                customRightComponent={
                  <div className="h-full w-[111px] flex-shrink-0">
                    <img
                      src={suite.image}
                      alt={suite.name}
                      className="h-full w-full object-cover"
                    />
                  </div>
                }
              />
            ))}
          </div>
        )}

        {activeTab === 'Agentic AI' && (
          <div className="grid w-full flex-1 grid-cols-1 gap-6 md:grid-cols-2">
            {marketplaceAgents.map(agent => (
              <AgentCard
                key={agent.id}
                agent={agent}
                link={ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardWelcome;
