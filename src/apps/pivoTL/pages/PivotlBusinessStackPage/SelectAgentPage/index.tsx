import React from 'react';
import AgentSelectionLayout from '../../../components/agents/AgentSelectionLayout';
import { useTenant } from '../../../context/TenantContext';
import { ROUTES } from '../../../constants/routes';
import { useNavigate } from 'react-router-dom';
import { businessStackBg } from '@/apps/pivoTL/assets/images';

const BusinessStackSelectAgentPage: React.FC = () => {
  const { tenants } = useTenant();
  const navigate = useNavigate();
  // Check if user has claimed the specific agent suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return (
      tenants &&
      tenants.length > 0 &&
      tenants.some(tenant => tenant.agentSuiteKey === suiteKey)
    );
  };

  return (
    <AgentSelectionLayout
      title="Seamless System Connections"
      description="Link agents to your CRM, communication tools, and workflows with secure per-agent authentication."
      bgImage={businessStackBg}
      pageType="business-stack"
      onAgentSuiteClick={suite => {
        if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
          navigate(
            ROUTES.DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE(suite.agentSuiteKey),
          );
        } else {
          navigate(ROUTES.DASHBOARD_BUSINESS_STACK);
        }
      }}
    />
  );
};

export default BusinessStackSelectAgentPage;
