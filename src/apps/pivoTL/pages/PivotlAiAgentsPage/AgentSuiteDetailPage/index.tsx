import React, { useEffect, useRef, useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { ROUTES } from '@/apps/pivoTL/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/apps/pivoTL/data/constants';
import { AgentCard } from '../index';
import {
  AIAgent,
  useClaimAgentSuiteApi,
} from '@/apps/pivoTL/services/upivotalAgenticService';
import EnhancedChatSidebar from '@/apps/pivoTL/components/common/EnhancedChatSidebar';
import { useActiveTenant } from '@/apps/pivoTL/context/ActiveTenantContext';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

const AgentSuiteDetailPage: React.FC = () => {
  const location = useLocation();
  const suite = location.state;

  // Get agents that belong to this suite
  const suiteAgents: AIAgent[] = suite?.availableAgents;
  const suiteFallbackImage = mockAgentsSuite.filter(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase(),
  )[0]?.image;

  // Use the first agent for the chat sidebar, always ensure there's an agent selected
  const [selectedAgent] = useState(() => {
    if (!suiteAgents) return null;
    // Prefer Scyra if available in this suite, otherwise use the first agent
    return (
      suiteAgents.find(agent => agent.agentKey === 'scyra') ||
      suiteAgents[0] ||
      null
    );
  });

  const [isLoading, setIsLoading] = useState(false);
  const { activeAgent, setActiveAgent } = useActiveTenant();

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  const handleClaimAgentSuite = async (agentSuiteKey: string) => {
    const claimAgentsSuite = useClaimAgentSuiteApi();
    try {
      setIsLoading(true);
      const response = await claimAgentsSuite(agentSuiteKey);
      console.log('response', response);
      if (response.status) {
        // TODO: Update the list of tenants using setTenants and then Navigate to ROUTES.DASHBOARD_AGENT_ACTIVATION_SUITE(suite.id)
      }
    } catch (error) {
      console.error('Error claiming AI agents suite:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Trigger reload of chat history when agent changes
  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error('Error occured while changing agent:', error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  if (!suite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Agent Suite Not Found
          </h1>
          <Link
            to={ROUTES.DASHBOARD_AI_AGENTS}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to Agents Hub
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      {suiteAgents.length > 0 && selectedAgent && selectedAgent.agentKey && (
        <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />
      )}

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-col gap-y-4 p-8">
          {/* Breadcrumb */}
          <Link
            to={ROUTES.DASHBOARD_AI_AGENTS}
            className="flex items-center gap-1 font-semibold text-blackTwo"
          >
            <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
            Agent Suite
          </Link>

          {/* Suite Header */}
          <div className="mb-6 font-spartan">
            <div
              className="h-48 rounded-lg bg-cover bg-center"
              style={{
                backgroundImage: `url(${suite.avatar || suiteFallbackImage})`,
              }}
            >
              <div className="flex h-full items-start justify-between p-6">
                <button className="rounded-md bg-[#F4F4F4] px-6 py-3">
                  <h1 className="text-3xl font-bold text-blackOne">
                    {suite.agentSuiteName}
                  </h1>
                </button>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="mt-6">
                <h2 className="text-2xl font-semibold text-blackOne">
                  {suite.description}
                </h2>
                <p className="font-inter text-lg text-subText">
                  {suite.roleDescription}
                </p>
              </div>
              <button
                // to={ROUTES.DASHBOARD_AGENT_ACTIVATION_SUITE(suite.id)}
                className="rounded-lg bg-primary px-6 py-3 font-normal text-white transition-colors hover:bg-orange-15"
                onClick={() => handleClaimAgentSuite(suite.agentSuiteKey)}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                    <span>Getting Started...</span>
                  </div>
                ) : (
                  'Get Started'
                )}
              </button>
            </div>
          </div>

          {/* Agents Grid */}
          {suiteAgents.length > 0 && (
            <div className="grid w-fit grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {suiteAgents.map(agent => (
                <AgentCard
                  key={agent.agentKey}
                  agent={agent}
                  showChatButton={true}
                  link={
                    agent.agentKey
                      ? ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(agent.agentKey)
                      : '#'
                  }
                  onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentSuiteDetailPage;
