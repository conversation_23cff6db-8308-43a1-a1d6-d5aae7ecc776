import React, { useState } from 'react';
import { IndividualAgent } from '../../../../data/constants';
import DocumentUploadCard from '../components/DocumentUploadCard';
import FileUploadModal from '../components/FileUploadModal';
import { ArrowLeftIcon, ArrowRight, ChevronDown } from 'lucide-react';
import clsx from 'clsx';

interface AgentLevelSetupProps {
  selectedAgent: string | null;
  uploadedDocuments: Record<string, File[]>;
  onDocumentsUpdate: (docs: Record<string, File[]>) => void;
  onNext: () => void;
  onBack: () => void;
  currentAgent?: IndividualAgent | null;
}

const AgentLevelSetup: React.FC<AgentLevelSetupProps> = ({
  uploadedDocuments,
  onDocumentsUpdate,
  onNext,
  onBack,
  currentAgent,
}) => {
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [activeUploadCategory, setActiveUploadCategory] = useState<string>('');
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] =
    useState<boolean>(false);

  const agentOptions = [
    { id: 'setiq', name: 'SetIQ', icon: currentAgent?.image },
    { id: 'other', name: 'Other Agent', icon: null },
  ];

  const uploadCategories = [
    {
      id: 'instructions',
      title: 'Agent Instructions',
      description: 'Special procedures and workflows for this agent.',
      icon: (
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.5 2C6.97717 2 2.5 6.47713 2.5 12C2.5 17.5228 6.97717 22 12.5 22C18.0229 22 22.5 17.5228 22.5 12C22.5 6.47713 18.0229 2 12.5 2ZM12.5 20C8.08881 20 4.50002 16.4112 4.50002 12C4.50002 7.58877 8.08877 4.00002 12.5 4.00002C16.9113 4.00002 20.5 7.58877 20.5 12C20.5 16.4112 16.9113 20 12.5 20ZM13.7522 8C13.7522 8.72506 13.2243 9.25002 12.5102 9.25002C11.7671 9.25002 11.2522 8.72502 11.2522 7.98612C11.2522 7.27597 11.7811 6.75003 12.5102 6.75003C13.2243 6.75003 13.7522 7.27597 13.7522 8ZM11.5022 11H13.5022V17H11.5022V11Z"
            fill="#FF3E00"
          />
        </svg>
      ),
    },
    {
      id: 'playbooks',
      title: 'Sample Playbooks',
      description: 'Example scripts and materials used in real scenarios.',
      icon: (
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M11.5 14H13.5C13.7833 14 14.021 13.904 14.213 13.712C14.405 13.52 14.5007 13.2827 14.5 13C14.4993 12.7173 14.4033 12.48 14.212 12.288C14.0207 12.096 13.7833 12 13.5 12H11.5C11.2167 12 10.9793 12.096 10.788 12.288C10.5967 12.48 10.5007 12.7173 10.5 13C10.4993 13.2827 10.5953 13.5203 10.788 13.713C10.9807 13.9057 11.218 14.0013 11.5 14ZM11.5 11H17.5C17.7833 11 18.021 10.904 18.213 10.712C18.405 10.52 18.5007 10.2827 18.5 10C18.4993 9.71733 18.4033 9.48 18.212 9.288C18.0207 9.096 17.7833 9 17.5 9H11.5C11.2167 9 10.9793 9.096 10.788 9.288C10.5967 9.48 10.5007 9.71733 10.5 10C10.4993 10.2827 10.5953 10.5203 10.788 10.713C10.9807 10.9057 11.218 11.0013 11.5 11ZM11.5 8H17.5C17.7833 8 18.021 7.904 18.213 7.712C18.405 7.52 18.5007 7.28267 18.5 7C18.4993 6.71733 18.4033 6.48 18.212 6.288C18.0207 6.096 17.7833 6 17.5 6H11.5C11.2167 6 10.9793 6.096 10.788 6.288C10.5967 6.48 10.5007 6.71733 10.5 7C10.4993 7.28267 10.5953 7.52033 10.788 7.713C10.9807 7.90567 11.218 8.00133 11.5 8ZM8.5 18C7.95 18 7.47933 17.8043 7.088 17.413C6.69667 17.0217 6.50067 16.5507 6.5 16V4C6.5 3.45 6.696 2.97933 7.088 2.588C7.48 2.19667 7.95067 2.00067 8.5 2H20.5C21.05 2 21.521 2.196 21.913 2.588C22.305 2.98 22.5007 3.45067 22.5 4V16C22.5 16.55 22.3043 17.021 21.913 17.413C21.5217 17.805 21.0507 18.0007 20.5 18H8.5ZM8.5 16H20.5V4H8.5V16ZM4.5 22C3.95 22 3.47933 21.8043 3.088 21.413C2.69667 21.0217 2.50067 20.5507 2.5 20V7C2.5 6.71667 2.596 6.47933 2.788 6.288C2.98 6.09667 3.21733 6.00067 3.5 6C3.78267 5.99933 4.02033 6.09533 4.213 6.288C4.40567 6.48067 4.50133 6.718 4.5 7V20H17.5C17.7833 20 18.021 20.096 18.213 20.288C18.405 20.48 18.5007 20.7173 18.5 21C18.4993 21.2827 18.4033 21.5203 18.212 21.713C18.0207 21.9057 17.7833 22.0013 17.5 22H4.5Z"
            fill="#FF3E00"
          />
        </svg>
      ),
    },
  ];

  const handleUploadClick = (categoryId: string) => {
    setActiveUploadCategory(categoryId);
    setUploadModalOpen(true);
  };

  const handleFilesUpload = (files: File[]) => {
    const updatedDocs = {
      ...uploadedDocuments,
      [activeUploadCategory]: [
        ...(uploadedDocuments[activeUploadCategory] || []),
        ...files,
      ],
    };
    onDocumentsUpdate(updatedDocs);
    setUploadModalOpen(false);
  };

  const handleFileRemove = (categoryId: string, fileIndex: number) => {
    const updatedDocs = {
      ...uploadedDocuments,
      [categoryId]:
        uploadedDocuments[categoryId]?.filter(
          (_, index) => index !== fileIndex,
        ) || [],
    };
    onDocumentsUpdate(updatedDocs);
  };

  return (
    <div className="mx-auto flex max-w-[640px] flex-col gap-6 rounded-xl bg-white p-6">
      {/* Header */}
      <div className="flex w-full flex-col gap-4 rounded-xl bg-[#363D8808] p-4">
        <div className="mx-auto flex w-full max-w-[510px] items-center justify-between gap-4">
          <h2 className="text-lg font-semibold text-blackOne">Agent Level</h2>

          {/* Suite Selector Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
              className="flex h-[48px] w-fit items-center gap-3 rounded-lg bg-white px-4 py-2 hover:border focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded bg-[#F9FAFB]">
                {currentAgent?.image ? (
                  <img
                    src={currentAgent.image}
                    alt="Suite Icon"
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-4 w-4 rounded bg-gray-300" />
                )}
              </div>
              <span className="text-sm font-semibold text-blackOne">
                {currentAgent?.name || 'SetIQ'}
              </span>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </button>

            {/* Dropdown Menu */}
            {isAgentDropdownOpen && (
              <div className="absolute right-0 top-full z-50 mt-1 w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]">
                <div className="py-1">
                  {agentOptions.map(agent => (
                    <button
                      key={agent.id}
                      onClick={() => {
                        setIsAgentDropdownOpen(false);
                      }}
                      className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm font-medium transition-colors hover:bg-gray-50"
                    >
                      <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded bg-[#F9FAFB]">
                        {agent.icon ? (
                          <img
                            src={agent.icon}
                            alt="Suite Icon"
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-4 w-4 rounded bg-gray-300" />
                        )}
                      </div>
                      {agent.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Upload Categories */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {uploadCategories.map(category => (
          <DocumentUploadCard
            key={category.id}
            title={category.title}
            description={category.description}
            icon={category.icon}
            uploadedFiles={uploadedDocuments[category.id] || []}
            onUploadClick={() => handleUploadClick(category.id)}
            onFileRemove={fileIndex => handleFileRemove(category.id, fileIndex)}
          />
        ))}
      </div>
      <div className="h-[1px] w-full bg-primary" />
      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="flex items-center gap-2 text-sm text-blackOne"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back
        </button>

        <button
          onClick={onNext}
          className={clsx(
            'bg-light-orangeTwo flex items-center gap-2 rounded-lg border border-primary px-6 py-2 font-medium text-blackOne transition-colors disabled:opacity-50',
          )}
        >
          Proceed
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>
      {/* File Upload Modal */}
      {uploadModalOpen && (
        <FileUploadModal
          isOpen={uploadModalOpen}
          onClose={() => setUploadModalOpen(false)}
          onFilesUpload={handleFilesUpload}
          title="Import your file"
        />
      )}
    </div>
  );
};

export default AgentLevelSetup;
