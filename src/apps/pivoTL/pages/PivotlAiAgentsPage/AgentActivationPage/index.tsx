import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { agentSuites, marketplaceAgents } from '../../../data/constants';
import AgentChatSidebar from '../../../components/common/AgentChatSidebar';
import { useScyraChat } from '../../../hooks/useScyraChat';
import AgentLevelSelector from './steps/AgentLevelSelector';
import SuiteLevelSetup from './steps/SuiteLevelSetup';
import AgentLevelSetup from './steps/AgentLevelSetup';

export type ActivationStep =
  | 'level-selector'
  | 'suite-setup'
  | 'agent-setup'
  | 'complete';
export type ActivationLevel = 'suite' | 'agent';

interface AgentActivationState {
  step: ActivationStep;
  level: ActivationLevel | null;
  selectedSuite: string | null;
  selectedAgent: string | null;
  uploadedDocuments: Record<string, File[]>;
}

const AgentActivationPage: React.FC = () => {
  const { suiteId, agentId } = useParams<{
    suiteId?: string;
    agentId?: string;
  }>();

  const [state, setState] = useState<AgentActivationState>({
    step: 'level-selector',
    level: null,
    selectedSuite: suiteId || null,
    selectedAgent: agentId || null,
    uploadedDocuments: {},
  });

  // Get the current suite/agent for context
  const currentSuite = suiteId ? agentSuites.find(s => s.id === suiteId) : null;
  const currentAgent = agentId
    ? marketplaceAgents.find(a => a.id === agentId)
    : null;

  // Use Scyra for the chat sidebar
  const selectedChatAgent =
    marketplaceAgents.find(agent => agent.id === 'scyra') ||
    marketplaceAgents[0];

  const { state: chatState, sendMessage, groupMessagesByDate } = useScyraChat();
  const groupedMessages = groupMessagesByDate();

  const updateState = (updates: Partial<AgentActivationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const handleNext = () => {
    switch (state.step) {
      case 'level-selector':
        if (state.level === 'suite') {
          updateState({ step: 'suite-setup' });
        } else if (state.level === 'agent') {
          updateState({ step: 'agent-setup' });
        }
        break;
      case 'suite-setup':
        updateState({ step: 'complete' });
        break;
      case 'agent-setup':
        updateState({ step: 'complete' });
        break;
    }
  };

  const handleBack = () => {
    switch (state.step) {
      case 'suite-setup':
      case 'agent-setup':
        updateState({ step: 'level-selector' });
        break;
      case 'complete':
        if (state.level === 'suite') {
          updateState({ step: 'suite-setup' });
        } else {
          updateState({ step: 'agent-setup' });
        }
        break;
    }
  };

  const renderStep = () => {
    switch (state.step) {
      case 'level-selector':
        return (
          <AgentLevelSelector
            selectedLevel={state.level}
            onLevelSelect={level => updateState({ level })}
            onNext={handleNext}
            currentSuite={currentSuite}
            currentAgent={currentAgent}
          />
        );
      case 'suite-setup':
        return (
          <SuiteLevelSetup
            selectedSuite={state.selectedSuite}
            uploadedDocuments={state.uploadedDocuments}
            onDocumentsUpdate={docs => updateState({ uploadedDocuments: docs })}
            onNext={handleNext}
            onBack={handleBack}
            currentSuite={currentSuite}
          />
        );
      case 'agent-setup':
        return (
          <AgentLevelSetup
            selectedAgent={state.selectedAgent}
            uploadedDocuments={state.uploadedDocuments}
            onDocumentsUpdate={docs => updateState({ uploadedDocuments: docs })}
            onNext={handleNext}
            onBack={handleBack}
            currentAgent={currentAgent}
          />
        );
      case 'complete':
        return (
          <div className="flex items-center justify-center py-16">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-blackOne">
                Agent Activation Complete!
              </h2>
              <p className="text-gray-600">
                Your agent has been successfully configured and activated.
              </p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <AgentChatSidebar
        agent={selectedChatAgent}
        state={chatState}
        sendMessage={sendMessage}
        groupedMessages={groupedMessages}
      />

      {/* Main Content - RIGHT SIDE */}
      {/* Step Content */}
      <div className="flex flex-1 flex-col items-center justify-center p-8">
        {renderStep()}
      </div>
    </div>
  );
};

export default AgentActivationPage;
