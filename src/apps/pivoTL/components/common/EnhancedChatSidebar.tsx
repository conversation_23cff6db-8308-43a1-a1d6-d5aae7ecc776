import { useMemo, useEffect, MutableRefObject } from 'react';
import { EnhancedScyraChatInterface } from '../chat/EnhancedScyraChatInterface';
import { ChatInput } from '../chat/ChatInput';
import { eclipse, halfEclipse } from '../../assets/images';
import { useScyraChat } from '../../hooks/useScyraChat';
import { ConnectionFlowContext } from '../../types/businessStack';

interface EnhancedChatSidebarProps {
  connectionFlow?: ConnectionFlowContext;
  reloadChatHistoryRef?: MutableRefObject<(() => Promise<void>) | null>;
}

const EnhancedChatSidebar = ({
  connectionFlow,
  reloadChatHistoryRef,
}: EnhancedChatSidebarProps) => {
  const {
    state: scyraChatState,
    sendMessage,
    groupMessagesByDate,
    reloadChatHistory,
  } = useScyraChat();
  const groupedMessages = groupMessagesByDate();

  // Expose reloadChatHistory to parent component
  useEffect(() => {
    if (reloadChatHistoryRef) {
      reloadChatHistoryRef.current = reloadChatHistory;
    }
  }, [reloadChatHistoryRef, reloadChatHistory]);

  // Create a chat input component that can be enhanced
  const ChatInputComponent = useMemo(() => {
    const EnhancedChatInput = (props: any) => (
      <ChatInput
        onSendMessage={props.onSendMessage || sendMessage}
        placeholder={props.placeholder || "I'm here — whenever you're ready."}
        disabled={props.disabled || scyraChatState.isLoading}
        {...props}
      />
    );
    return EnhancedChatInput;
  }, [sendMessage, scyraChatState.isLoading]);

  return (
    <div className="relative flex h-96 flex-col bg-gradient-to-br from-orange-50/50 to-orange-100 px-6 py-4 md:h-full md:w-1/3">
      {/* Enhanced Scyra Chat Interface */}
      <div className="z-10 flex-1 overflow-hidden rounded-2xl bg-white bg-opacity-40">
        <EnhancedScyraChatInterface
          state={scyraChatState}
          ChatInputComponent={ChatInputComponent}
          groupedMessages={groupedMessages}
          connectionFlow={connectionFlow}
          originalSendMessage={sendMessage}
        />
      </div>

      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />
    </div>
  );
};

export default EnhancedChatSidebar;
