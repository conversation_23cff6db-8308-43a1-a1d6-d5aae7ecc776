import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Download,
  FileText,
} from 'lucide-react';

interface ActionDropdownProps {
  actions?: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
    variant?: 'default' | 'danger';
  }>;
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({ actions }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const defaultActions = [
    {
      label: 'View',
      icon: <Eye className="h-4 w-4" />,
      onClick: () => console.log('View'),
      variant: 'default' as const,
    },
    {
      label: 'Replace',
      icon: <Download className="h-4 w-4" />,
      onClick: () => console.log('Replace'),
      variant: 'default' as const,
    },
    {
      label: 'Edit',
      icon: <Edit className="h-4 w-4" />,
      onClick: () => console.log('Edit'),
      variant: 'default' as const,
    },
    {
      label: 'Delete',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: () => console.log('Delete'),
      variant: 'danger' as const,
    },
    {
      label: 'Audit Log',
      icon: <FileText className="h-4 w-4" />,
      onClick: () => console.log('Audit Log'),
      variant: 'default' as const,
    },
  ];

  const menuActions = actions || defaultActions;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="rounded-full p-1 text-gray-400 transition-colors duration-150 hover:bg-gray-100 hover:text-gray-600"
      >
        <MoreVertical className="h-4 w-4" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.1 }}
            className="absolute right-0 z-[999999] mt-2 w-48 rounded-xl border border-gray-200 bg-white shadow-lg"
          >
            <div className="py-1">
              {menuActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => {
                    action.onClick();
                    setIsOpen(false);
                  }}
                  className={`flex w-full items-center px-4 py-2 text-sm transition-colors duration-150 ${
                    action.variant === 'danger'
                      ? 'text-red-600 hover:bg-red-50'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {action.icon}
                  <span className="ml-2">{action.label}</span>
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ActionDropdown;
