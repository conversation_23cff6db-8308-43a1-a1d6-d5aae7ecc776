import { ArrowRight } from 'lucide-react';
import React from 'react';
import { useNavigate } from 'react-router-dom';

interface SectionTitleProps {
  title: string;
  showBrowseAll?: boolean;
  onBrowseAll?: () => void;
  browseAllRoute?: string;
  className?: string;
}

const SectionTitle: React.FC<SectionTitleProps> = ({
  title,
  showBrowseAll = true,
  onBrowseAll,
  browseAllRoute,
  className = '',
}) => {
  const navigate = useNavigate();

  const handleBrowseAll = () => {
    if (onBrowseAll) {
      onBrowseAll();
    } else if (browseAllRoute) {
      navigate(browseAllRoute);
    }
  };

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <h2 className="font-semibold text-blackOne sm:text-lg">{title}</h2>
      {showBrowseAll && (
        <button
          className="flex items-center gap-2 border-b border-primary pb-2 text-sm font-normal text-primary"
          onClick={handleBrowseAll}
        >
          Browse all <ArrowRight className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default SectionTitle;
