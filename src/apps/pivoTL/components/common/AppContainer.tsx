import React from 'react';
import classNames from 'classnames';

const AppContainer = ({
  children,
  className,
  isPadding = true,
}: {
  children: React.ReactNode;
  className?: string;
  isPadding?: boolean;
}) => {
  return (
    <div
      className={classNames(
        'max-w-[1640px]',
        isPadding ? 'p-4 md:p-8' : 'p-0',
        className,
      )}
    >
      {children}
    </div>
  );
};

export default AppContainer;
