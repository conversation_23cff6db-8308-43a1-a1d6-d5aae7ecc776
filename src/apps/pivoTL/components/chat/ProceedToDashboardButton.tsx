interface ProceedToDashboardButtonProps {
  onProceedToDashboard: () => void;
  disabled?: boolean;
}

export const ProceedToDashboardButton = ({
  onProceedToDashboard,
  disabled = false,
}: ProceedToDashboardButtonProps) => {
  return (
    <div className="mt-3 flex justify-start">
      <button
        onClick={onProceedToDashboard}
        disabled={disabled}
        className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-orange-15 disabled:cursor-not-allowed disabled:opacity-50"
      >
        Proceed to Dashboard
      </button>
    </div>
  );
};
