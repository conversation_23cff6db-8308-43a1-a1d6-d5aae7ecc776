interface ChatSkeletonProps {
  messageCount?: number;
}

const MessageSkeleton = () => {
  return (
    <div className="mb-6 flex animate-pulse gap-3">
      {/* Avatar Skeleton */}
      <div className="h-10 w-10 flex-shrink-0 rounded-full bg-gray-200"></div>

      {/* Message Content Skeleton */}
      <div className="flex-1">
        {/* Header Skeleton */}
        <div className="mb-1 flex items-center gap-2">
          <div className="h-4 w-16 rounded bg-gray-200"></div>
          <div className="bg-gray-150 h-3 w-12 rounded"></div>
        </div>

        {/* Message Text Skeleton */}
        <div className="rounded-lg bg-gray-5 p-3">
          <div className="space-y-2">
            <div className="h-4 w-full rounded bg-gray-200"></div>
            <div className="h-4 w-3/4 rounded bg-gray-200"></div>
            <div className="h-4 w-1/2 rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ChatSkeleton = ({ messageCount = 3 }: ChatSkeletonProps) => {
  return (
    <div className="px-4 py-4">
      {/* Date Header Skeleton */}
      <div className="mb-2 text-center">
        <div className="mx-auto h-3 w-16 animate-pulse rounded bg-gray-200"></div>
      </div>

      {/* Message Skeletons */}
      {Array.from({ length: messageCount }).map((_, index) => (
        <MessageSkeleton key={index} />
      ))}
    </div>
  );
};
