import { ArrowLeft } from 'lucide-react';
import { marketplaceAgents } from '../../data/constants';

interface MarketplaceAgentsSidebarProps {
  activeAgent: (typeof marketplaceAgents)[0];
  onAgentSelect: (agent: (typeof marketplaceAgents)[0]) => void;
}

export const MarketplaceAgentsSidebar = ({
  activeAgent,
  onAgentSelect,
}: MarketplaceAgentsSidebarProps) => {
  return (
    <div className="sticky top-8 rounded-lg">
      {marketplaceAgents.map((agent, index) => (
        <div
          key={index}
          className={`mb-4 flex h-[120px] w-96 cursor-pointer items-center gap-4 rounded-lg border transition-colors ${
            activeAgent.name === agent.name
              ? 'border-primary'
              : 'border-grayTwentyThree hover:border-primary/50'
          }`}
          onClick={() => onAgentSelect(agent)}
        >
          <div className="h-full rounded-l-lg bg-peachTwo">
            <img
              src={agent.image}
              className="h-[95%] object-cover"
              alt={agent.name}
            />
          </div>
          <div className="flex flex-col gap-2">
            <p className="font-bold text-darkGray">{agent.name}</p>
            <p className="text-sm font-medium text-blackTwo">
              {agent.category}
            </p>
            <button className="flex items-center gap-2 rounded-md bg-primary px-3 py-2 text-sm font-medium text-white transition-colors hover:bg-orange-15">
              <ArrowLeft />
              Chat with {agent.name} for more
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};
