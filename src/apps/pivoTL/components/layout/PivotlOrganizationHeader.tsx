import React from 'react';
import PivotlLogo from '../ui/PivotlLogo';
import Button from '@/components/ui/ButtonComponent';
import { UserDropdown } from '../ui/UserDropdown';

interface PivotlOrganizationHeaderProps {
  showUpgradeButton?: boolean;
  showUserDropdown?: boolean;
  className?: string;
}

const PivotlOrganizationHeader: React.FC<PivotlOrganizationHeaderProps> = ({
  showUpgradeButton = true,
  showUserDropdown = true,
  className = '',
}) => {
  return (
    <header
      className={`flex items-center justify-between border-b border-gray-200 px-6 py-4 ${className}`}
    >
      {/* Logo */}
      <div className="flex items-center">
        <PivotlLogo />
      </div>

      {/* Right side - Organization Dropdown, Upgrade and Profile */}
      <div className="flex items-center space-x-4">
        {showUpgradeButton && (
          <Button className="h-[44px] rounded-lg border border-primary bg-white p-4 text-primary">
            Upgrade
          </Button>
        )}
        {showUserDropdown && <UserDropdown />}
      </div>
    </header>
  );
};

export default PivotlOrganizationHeader;
