import { Outlet } from 'react-router-dom';
import { PivotlNavbar } from './PivotlNavbar';
import { PivotlFooter } from './PivotlFooter';
import { PivotlErrorBoundary } from '../ui/PivotlErrorBoundary';

export const PivotlNavigationShell = () => {
  return (
    <PivotlErrorBoundary>
      <div className="flex min-h-screen flex-col">
        <PivotlNavbar />
        <main className="flex-1">
          <Outlet />
        </main>
        <PivotlFooter />
      </div>
    </PivotlErrorBoundary>
  );
};
