import { Outlet } from 'react-router-dom';
import { <PERSON>votlFooter } from './PivotlFooter';
import { PivotlErrorBoundary } from '../ui/PivotlErrorBoundary';
import { PivotlAuthProvider } from '../../context/PivotlAuthContext';
import { ActiveTenantProvider } from '../../context/ActiveTenantContext';

export const PivotlOnboardingShell = () => {
  return (
    <PivotlAuthProvider>
      <ActiveTenantProvider>
        <PivotlErrorBoundary>
          <div className="flex min-h-screen flex-col">
            <main className="flex-1">
              <Outlet />
            </main>
            <PivotlFooter />
          </div>
        </PivotlErrorBoundary>
      </ActiveTenantProvider>
    </PivotlAuthProvider>
  );
};
