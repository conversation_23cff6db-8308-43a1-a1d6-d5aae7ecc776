import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { useActiveTenant } from '../../context/ActiveTenantContext';
import clsx from 'clsx';
import { scyraLogo } from '../../assets/images';
import { AIAgent, useAIAgentsApi } from '../../services/upivotalAgenticService';

const AgentsDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [agents, setAgents] = useState<AIAgent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { activeAgent, setActiveAgent } = useActiveTenant();

  const getAIAgents = useAIAgentsApi();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Load agents on component mount
  useEffect(() => {
    const loadAgents = async () => {
      try {
        setIsLoading(true);
        const response = await getAIAgents();
        if (response.status && response.data.aiAgents) {
          // Filter only enabled agents
          const enabledAgents = response.data.aiAgents.filter(
            agent => agent.enabled,
          );
          setAgents(enabledAgents);

          // Set first agent as default if no active agent is set
          if (!activeAgent && enabledAgents.length > 0) {
            setActiveAgent(enabledAgents[0].agentKey);
          }
        }
      } catch (error) {
        console.error('Error loading AI agents:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAgents();
  }, []);

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
    setIsOpen(false);
  };

  // Get current agent display info
  const getCurrentAgent = () => {
    if (!activeAgent) return null;
    return agents.find(agent => agent.agentKey === activeAgent);
  };

  const currentAgent = getCurrentAgent();

  // Show loading state
  if (isLoading) {
    return (
      <div className="relative">
        <div
          className="flex animate-pulse items-center gap-3 rounded-lg border border-gray-300 bg-white px-4 py-2"
          style={{ width: '203px' }}
        >
          <div className="h-7 w-7 rounded-lg bg-gray-300"></div>
          <div className="h-4 w-24 rounded bg-gray-300"></div>
          <ChevronDown className="ml-auto h-4 w-4 text-gray-400" />
        </div>
      </div>
    );
  }

  // Don't render if no agents available
  if (agents.length === 0) {
    return null;
  }

  return (
    <div ref={dropdownRef} className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-3 rounded-lg border border-grayTwentyEight bg-white p-2 text-primary transition-all hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/20"
        style={{ width: '203px' }}
      >
        <div className="h-7 w-7 overflow-hidden rounded-full">
          <img
            src={currentAgent?.avatar || scyraLogo}
            alt={`${currentAgent?.agentName || 'Agent'} Icon`}
            className="h-full w-full object-cover"
            onError={e => {
              // Fallback to scyraLogo if agent avatar fails to load
              (e.target as HTMLImageElement).src = scyraLogo;
            }}
          />
        </div>
        <span className="flex-1 truncate text-left text-xl font-semibold text-blackOne">
          {currentAgent?.agentName || 'Select Agent'}
        </span>
        <ChevronDown
          className={`h-4 w-4 flex-shrink-0 text-gray-500 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full z-50 mt-2 rounded-xl border border-gray-200 bg-white shadow-lg"
            style={{ width: '203px' }}
          >
            {agents.map((agent, index) => {
              const isSelected = activeAgent === agent.agentKey;
              return (
                <button
                  key={agent.agentKey}
                  onClick={() => handleAgentSelect(agent.agentKey)}
                  className={clsx(
                    'flex w-full items-center gap-3 p-2.5 text-left text-sm transition-colors hover:bg-gray-50',
                    isSelected ? 'bg-[#E8E7E4]' : 'text-gray-700',
                    index === agents.length - 1 ? 'border-b-0' : 'border-b',
                    index === 0 ? 'rounded-t-lg' : '',
                    index === agents.length - 1
                      ? 'rounded-b-lg'
                      : 'rounded-b-none',
                  )}
                >
                  <img
                    src={agent.avatar || scyraLogo}
                    alt={`${agent.agentName} Icon`}
                    className="h-7 w-7 rounded-lg object-cover"
                    onError={e => {
                      // Fallback to scyraLogo if agent avatar fails to load
                      (e.target as HTMLImageElement).src = scyraLogo;
                    }}
                  />
                  <div className="flex min-w-0 flex-1 flex-col">
                    <span
                      className={`truncate font-medium ${isSelected ? 'text-gray-900' : 'text-gray-700'}`}
                    >
                      {agent.agentName}
                    </span>
                  </div>
                </button>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AgentsDropdown;
