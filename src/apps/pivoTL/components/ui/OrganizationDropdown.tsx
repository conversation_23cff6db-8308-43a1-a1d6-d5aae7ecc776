import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { useTenant } from '../../context/TenantContext';
import { useActiveTenant } from '../../context/ActiveTenantContext';
import { usePivotlAuth } from '../../context/PivotlAuthContext';
import OrganizationDropdownSkeleton from './OrganizationDropdownSkeleton';
import clsx from 'clsx';
import { organizationPlaceholderDrLogo } from '../../assets/images';

const OrganizationDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const {
    tenants: organizations,
    selectedTenant: selectedOrganization,
    setSelectedTenant: setSelectedOrganization,
  } = useTenant();
  const { activeTenant, setActiveTenant } = useActiveTenant();
  const { tenants, isLoading } = usePivotlAuth();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Sync active tenant with selected organization
  useEffect(() => {
    if (
      selectedOrganization?.tenant &&
      activeTenant?.tenantId !== selectedOrganization.tenant.tenantId
    ) {
      setActiveTenant(selectedOrganization.tenant);
    }
  }, [selectedOrganization, activeTenant, setActiveTenant]);

  const handleOrganizationSelect = (organization: any) => {
    setSelectedOrganization(organization);
    if (organization.tenant) {
      setActiveTenant(organization.tenant);
      // Reload page to refresh all API calls with new tenant
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
    setIsOpen(false);
  };

  // Show skeleton while loading
  if (isLoading) {
    return <OrganizationDropdownSkeleton />;
  }

  // Always show dropdown if we have tenants, even for single tenant
  // This allows users to see their current tenant and access tenant info
  if (!tenants || tenants.length === 0 || !selectedOrganization) {
    return null;
  }

  // Get current tenant display name
  const getCurrentTenantName = () => {
    if (activeTenant) {
      return activeTenant.tenantAgentSuite.agentSuiteName;
    }
    if (selectedOrganization?.tenant) {
      return selectedOrganization.tenant.tenantAgentSuite.agentSuiteName;
    }
    return selectedOrganization?.name || 'Select Tenant';
  };

  return (
    <div ref={dropdownRef} className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-3 rounded-lg border border-primary bg-white p-2 text-primary transition-all hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/20"
        style={{ width: '203px' }}
      >
        <div className="h-7 w-7 overflow-hidden rounded-lg">
          <img
            src={organizationPlaceholderDrLogo}
            alt="Organization Icon"
            className="h-full w-full object-cover"
          />
        </div>
        <span className="flex-1 truncate text-left font-medium text-blackOne">
          {getCurrentTenantName()}
        </span>
        <ChevronDown
          className={`h-4 w-4 flex-shrink-0 text-gray-500 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full z-50 mt-2 rounded-xl border border-gray-200 bg-white shadow-lg"
            style={{ width: '203px' }}
          >
            {organizations.map((organization, index) => {
              const isSelected = selectedOrganization.id === organization.id;
              const tenant = organization.tenant;
              return (
                <button
                  key={organization.id}
                  onClick={() => handleOrganizationSelect(organization)}
                  className={clsx(
                    'flex w-full items-center gap-3 p-2.5 text-left text-sm transition-colors hover:bg-gray-50',
                    isSelected ? 'bg-[#E8E7E4]' : 'text-gray-700',
                    index === organizations.length - 1
                      ? 'border-b-0'
                      : 'border-b',
                    index === 0 ? 'rounded-t-lg' : '',
                    index === organizations.length - 1
                      ? 'rounded-b-lg'
                      : 'rounded-b-none',
                  )}
                >
                  <img
                    src={organizationPlaceholderDrLogo}
                    alt="Organization Icon"
                    className="h-7 w-7"
                  />
                  <div className="flex min-w-0 flex-1 flex-col">
                    <span
                      className={`truncate font-medium ${isSelected ? 'text-blackOne' : 'text-gray-700'}`}
                    >
                      {tenant?.tenantAgentSuite.agentSuiteName ||
                        organization.name}
                    </span>
                  </div>
                </button>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OrganizationDropdown;
