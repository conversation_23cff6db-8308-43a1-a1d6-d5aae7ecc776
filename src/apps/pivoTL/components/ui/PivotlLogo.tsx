import { Link } from 'react-router-dom';
import { pivotlLogo } from '../../assets/icons';
import { pivotlRoundedLogo } from '../../assets/images';
import { ROUTES } from '../../constants/routes';

interface PivotlLogoProps {
  variant?: 'dark' | 'light';
  rounded?: boolean;
}

const PivotlLogo = ({ variant = 'dark', rounded = false }: PivotlLogoProps) => {
  const textColor = variant === 'dark' ? 'text-blackOne' : 'text-white';

  return (
    <Link to={ROUTES.PIVOTL_HOME}>
      <div className="flex items-center space-x-2">
        <img
          className="h-8 w-8 rounded sm:h-10 sm:w-10"
          loading="lazy"
          src={rounded ? pivotlRoundedLogo : pivotlLogo}
        />
        <span
          className={`font-space-mono text-xl font-bold text-blackTwo sm:text-3xl ${textColor}`}
        >
          PivoTL
        </span>
      </div>
    </Link>
  );
};

export default PivotlLogo;
