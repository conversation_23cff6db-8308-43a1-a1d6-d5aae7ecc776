import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePivotlAuth } from '../../context/PivotlAuthContext';
import { useActiveTenant } from '../../context/ActiveTenantContext';
import { useTenant } from '../../context/TenantContext';
import { ROUTES } from '../../constants/routes';
import PivotlLogo from '../ui/PivotlLogo';

interface LoginRedirectHandlerProps {
  defaultRedirectPath?: string;
}

export const LoginRedirectHandler: React.FC<LoginRedirectHandlerProps> = ({
  defaultRedirectPath = ROUTES.DASHBOARD_BASE,
}) => {
  const navigate = useNavigate();
  const { tenants, isLoading, isError, isAuthenticated } = usePivotlAuth();
  const { setActiveTenant } = useActiveTenant();
  const {
    setSelectedTenant,
    setTenants,
  } = useTenant();

  useEffect(() => {
    if (!isAuthenticated) {
      // If not authenticated, redirect to login
      navigate(ROUTES.PIVOTL_LOGIN, { replace: true });
      return;
    }

    if (isLoading) {
      // Still loading, wait
      return;
    }

    if (isError) {
      // Error occurred, redirect to login
      navigate(ROUTES.PIVOTL_LOGIN, { replace: true });
      return;
    }

    if (tenants) {
      if (tenants.length === 0) {
        // No tenants available, redirect to default path
        navigate(defaultRedirectPath, { replace: true });
      } else if (tenants.length === 1) {
        // Single tenant, auto-select it and store in context
        const tenant = tenants[0];
        setActiveTenant(tenant);

        // Also set up organizations context for consistency
        const tenantData = {
          id: tenant.tenantId,
          agentSuiteKey: tenant.tenantAgentSuite.agentSuiteKey,
          name: tenant.tenantAgentSuite.agentSuiteName,
          logo: tenant.tenantAgentSuite.avatar,
          tenant: tenant,
        };
        setTenants([tenantData]);
        setSelectedTenant(tenantData);

        // Redirect directly to dashboard
        navigate(ROUTES.DASHBOARD_BASE, { replace: true });
      } else {
        // Multiple tenants, set up organizations context and redirect to selection page
        const tenantsData = tenants.map(tenant => ({
          id: tenant.tenantId,
          agentSuiteKey: tenant.tenantAgentSuite.agentSuiteKey,
          name: tenant.tenantAgentSuite.agentSuiteName,
          logo: tenant.tenantAgentSuite.avatar,
          tenant: tenant,
        }));
        setTenants(tenantsData);

        navigate(ROUTES.PIVOTL_ORGANIZATION_SELECTION, { replace: true });
      }
    }
  }, [
    isAuthenticated,
    isLoading,
    isError,
    tenants,
    navigate,
    defaultRedirectPath,
    setActiveTenant,
    setTenants,
    setSelectedTenant,
  ]);

  // Show loading state while determining redirect
  return (
    <div className="flex min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 font-inter">
      {/* Logo at top left */}
      <div className="absolute left-6 top-6">
        <PivotlLogo />
      </div>

      {/* Loading content */}
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
          <p className="text-lg text-gray-600">Setting up your workspace...</p>
        </div>
      </div>
    </div>
  );
};

export default LoginRedirectHandler;
