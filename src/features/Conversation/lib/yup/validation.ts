import { conversationVisibility } from '@/data/constants';
import { Helper } from '@/utils/helpers';
import * as yup from 'yup';

export const addConversationMessageSchema = yup.object().shape({
  content: yup.string(),
  files: yup.mixed<File>(),
  messageRepliedToId: yup.string(),
});

export const addFeedTopicCommentSchema = yup.object().shape({
  content: yup.string(),
  files: yup.mixed<File>(),
});
export const addFeedTopicCommentReplySchema = yup.object().shape({
  content: yup.string(),
  files: yup.mixed<File>(),
});
export const addTopicSchema = yup.object().shape({
  content: yup.string(),
  images: yup.mixed<File[]>(),
  videos: yup.mixed<File[]>(),
});

export const requestNoteSchema = yup.object().shape({
  requestNote: yup.string(),
});

export const iInviteToChannelSchema = yup.object().shape({
  inviteEmails: yup
    .array()
    .of(
      yup.object({
        inviteEmail: yup
          .string()
          .email('Please enter a valid email')
          .required('Email is a required field'),
      }),
    )
    .max(10, 'Pick a maximum of Ten requirements')
    .required('Email is a required field'),
});

export const addConversationStepOneSchema = yup.object().shape({
  channelName: yup
    .string()
    .max(50, 'Maximum character limit reached')
    .required('Channel Name is a required field'),
  channelDescription: yup
    .string()
    .max(300, 'Maximum character limit reached')
    .required('Channel Description is a required field'),

  channelVisibility: yup
    .string()
    .oneOf([
      conversationVisibility.PRIVATE,
      conversationVisibility.PUBLIC,
      conversationVisibility.WORKPLACE,
    ])
    .required('Channel Visibility Settings is a required field'),
  channelImage: yup.mixed<File>(),
  channelBannerImage: yup.mixed<File>(),
  focusCountries: yup
    .array()
    .of(yup.string().required('Supported Countries is a required field'))
    .min(1, 'Pick a minimum of one supported countries')
    .max(5, 'Pick a maximum of five supported countries')
    .required('Supported Countries is a required field'),
  industryInterests: yup
    .array()
    .of(yup.string().required('Industry is a required field'))
    .min(1, 'Pick a minimum of one industries')
    .max(2, 'Pick a maximum of two industries')
    .required('Industry is a required field'),
  sdgCategories: yup
    .array()
    .of(yup.string().required('SDG is a required field'))
    .min(1, 'Pick a minimum of one categories')
    .max(5, 'Pick a maximum of five categories')
    .required('SDG is a required field'),
});
export const updateConversationStepOneSchema = yup.object().shape({
  channelName: yup
    .string()
    .max(50, 'Maximum character limit reached')
    .required('Channel Name is a required field'),
  channelDescription: yup
    .string()
    .max(300, 'Maximum character limit reached')
    .required('Channel Description is a required field'),
  channelVisibility: yup
    .string()
    .oneOf([
      conversationVisibility.PRIVATE,
      conversationVisibility.PUBLIC,
      conversationVisibility.WORKPLACE,
    ])
    .required('Channel Visibility Settings is a required field'),

  focusCountries: yup
    .array()
    .of(yup.string().required('Supported Countries is a required field'))
    .min(1, 'Pick a minimum of one supported countries')
    .max(5, 'Pick a maximum of five supported countries'),
  industryInterests: yup
    .array()
    .of(yup.string().required('Industry is a required field'))
    .min(1, 'Pick a minimum of one industries')
    .max(2, 'Pick a maximum of two industries'),
  sdgCategories: yup
    .array()
    .of(yup.string().required('SDG is a required field'))
    .min(1, 'Pick a minimum of one categories')
    .max(5, 'Pick a maximum of five categories'),
  channelImage: yup.mixed<File>(),
  channelBannerImage: yup.mixed<File>(),
});
export const addConversationStepTwoSchema = yup.object().shape({
  membersEmails: yup
    .array()
    .typeError('Email must be a list seperated by commas')
    .of(
      yup
        .string()
        .required('Email is a required field')
        .matches(Helper.emailRegExp, 'Invalid email format'),
    )
    .min(2, 'At least two email is required')
    .required('Email is a required field'),
});
