import { useState, useEffect, useMemo, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { ProblemStatementResponse, CountryOption } from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import { PencilLine, X, ChevronDown } from 'lucide-react';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useUpdateProblemStatement } from '../../../hooks/apiQueryHooks/launchItQueryHooks';
import { UpdateProblemStatementResponse } from '../../../services/launchItApiRequests';
import { useCustomToast } from '@/hooks/useToast';
import { Helper } from '@/utils/helpers';
import { countryFlags } from '@/utils/helpers/country-flags-data';
import { sdgGoalsCategories } from '../../../data/constants';
import { Spinner } from '@/components/ui/CommonWidget/Loader';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
  onUpdateStatusChange?: (isUpdating: boolean) => void;
}

interface DescriptionFormData {
  title: string;
  countries: string[];
  description: string;
  descriptionVideoUrl: string;
  descriptionFile: File;
  focusCountries?: CountryOption[];
}

export default function DescriptionTabContent({
  data,
  onUpdateStatusChange,
}: DescriptionTabContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(data.title);
  const [editedDescription, setEditedDescription] = useState(data.description);
  const { successToast, errorToast } = useCustomToast();

  // API mutation for updating problem statement
  const { mutate: updateProblemStatement, isPending: isUpdating } =
    useUpdateProblemStatement({
      onSuccess: (response: UpdateProblemStatementResponse) => {
        successToast(
          response?.message || 'Problem statement updated successfully!',
        );
        setIsEditing(false);
      },
      onError: (error: any) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to update problem statement. Please try again.',
        );
      },
    });

  useEffect(() => {
    onUpdateStatusChange?.(isUpdating);
  }, [isUpdating, onUpdateStatusChange]);

  // Focus Countries state
  const [selectedCountries, setSelectedCountries] = useState<
    Array<{ code: string; name: string }>
  >(data.focusCountries || []);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [countryError, setCountryError] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isFormInitialized = useRef(false);

  // Goals Categories state
  const [selectedGoalsCategories, setSelectedGoalsCategories] = useState<
    Array<{ categoryRef: string; categoryName: string }>
  >([]);
  const [showGoalsCategoriesDropdown, setShowGoalsCategoriesDropdown] =
    useState(false);
  const [isGoalsCategoriesInitialized, setIsGoalsCategoriesInitialized] =
    useState(false);
  const goalsCategoriesDropdownRef = useRef<HTMLDivElement>(null);

  const { data: countries } = useGetCountries();
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );

  // Preselect values from API response data - memoized to prevent infinite re-renders
  const preselectedCountries = useMemo(
    () =>
      countryOptions.filter(option =>
        data.focusCountries.some(country => country.name === option.value),
      ),
    [countryOptions, data.focusCountries],
  );

  // Initialize Goals Categories from API response data
  const preselectedGoalsCategories = useMemo(() => {
    if (!data.goalsCategories || data.goalsCategories.length === 0) return [];

    return data.goalsCategories.map(category => {
      const categoryData = sdgGoalsCategories.find(
        sdgCategory => sdgCategory.categoryRef === category.id,
      );
      return {
        categoryRef: category.id,
        categoryName: categoryData?.categoryName || category.name,
      };
    });
  }, [data.goalsCategories]);

  const { reset } = useForm<DescriptionFormData>({
    defaultValues: {
      focusCountries: [],
    },
  });

  // Initialize Goals Categories
  useEffect(() => {
    if (
      preselectedGoalsCategories.length > 0 &&
      !isGoalsCategoriesInitialized
    ) {
      setSelectedGoalsCategories(preselectedGoalsCategories);
      setIsGoalsCategoriesInitialized(true);
    }
  }, [preselectedGoalsCategories, isGoalsCategoriesInitialized]);

  // Update form when preselected countries are available
  useEffect(() => {
    if (preselectedCountries.length > 0 && !isFormInitialized.current) {
      reset({
        focusCountries: preselectedCountries,
      });
      isFormInitialized.current = true;
    }
  }, [preselectedCountries]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    const payload = {
      problemStatementRef: data.problemStatementRef,
      title: editedTitle,
      refinedDescription: editedDescription,
      countries: selectedCountries.map(country => country.name),
      budget: parseFloat(data.budget.replace(/[$,]/g, '')) || 0,
      status: data.status.toUpperCase(),
      categoryRefs: selectedGoalsCategories.map(
        category => category.categoryRef,
      ),
    };

    updateProblemStatement(payload);
  };

  // Focus Countries helper functions
  const handleAddCountry = (countryName: string) => {
    setCountryError('');

    // Check if country is already selected
    if (selectedCountries.some(country => country.name === countryName)) {
      setCountryError('Country is already selected');
      return;
    }

    // Check maximum limit
    if (selectedCountries.length >= 5) {
      setCountryError('Maximum 5 countries can be selected');
      return;
    }

    // Find the country data from the countries data
    const countryData = countries?.data?.countries?.find(
      c => c.shortName === countryName,
    );
    if (countryData && countryData.shortName && countryData.countryCode) {
      setSelectedCountries(prev => [
        ...prev,
        { code: countryData.countryCode!, name: countryData.shortName! },
      ]);
    }
    setShowCountryDropdown(false);
  };

  const handleRemoveCountry = (countryName: string) => {
    setSelectedCountries(prev =>
      prev.filter(country => country.name !== countryName),
    );
    setCountryError('');
  };

  // Get available countries for dropdown (excluding already selected ones)
  const availableCountries = useMemo(() => {
    if (!countries?.data?.countries) return [];
    return countries.data.countries.filter(
      country =>
        country.shortName &&
        country.countryCode &&
        !selectedCountries.some(
          selected => selected.name === country.shortName,
        ),
    );
  }, [countries?.data?.countries, selectedCountries]);

  // Validate countries selection
  useEffect(() => {
    if (selectedCountries.length === 0) {
      setCountryError('At least one country must be selected');
    } else {
      setCountryError('');
    }
  }, [selectedCountries]);

  // Goals Categories helper functions
  const handleAddGoalsCategory = (categoryRef: string) => {
    // Check if category is already selected
    if (
      selectedGoalsCategories.some(
        category => category.categoryRef === categoryRef,
      )
    ) {
      return;
    }

    // Find the category data from sdgGoalsCategories
    const categoryData = sdgGoalsCategories.find(
      category => category.categoryRef === categoryRef,
    );

    if (categoryData) {
      setSelectedGoalsCategories(prev => [
        ...prev,
        {
          categoryRef: categoryData.categoryRef,
          categoryName: categoryData.categoryName,
        },
      ]);
    }
    setShowGoalsCategoriesDropdown(false);
  };

  const handleRemoveGoalsCategory = (categoryRef: string) => {
    setSelectedGoalsCategories(prev =>
      prev.filter(category => category.categoryRef !== categoryRef),
    );
  };

  // Get available categories for dropdown (excluding already selected ones)
  const availableGoalsCategories = useMemo(() => {
    return sdgGoalsCategories.filter(
      category =>
        !selectedGoalsCategories.some(
          selected => selected.categoryRef === category.categoryRef,
        ),
    );
  }, [selectedGoalsCategories]);

  // Click outside handler for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowCountryDropdown(false);
      }

      if (
        goalsCategoriesDropdownRef.current &&
        !goalsCategoriesDropdownRef.current.contains(event.target as Node)
      ) {
        setShowGoalsCategoriesDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* Edit Button */}
      <div className="-mt-[72px] flex justify-end">
        {!isEditing ? (
          <Button
            onClick={handleEdit}
            className="flex items-center gap-2 bg-grayNine px-4 py-2 text-white hover:bg-primary"
          >
            Edit
            <PencilLine size={20} className="-mt-0.5" />
          </Button>
        ) : (
          <div className="">
            <Button
              onClick={handleSave}
              disabled={isUpdating}
              className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isUpdating ? (
                <div className="flex items-center gap-2">
                  <Spinner className="h-4 w-4 border-primary border-b-[transparent]" />
                  <span>Saving...</span>
                </div>
              ) : (
                'Save'
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        {!isEditing ? (
          <p className="mb-4 rounded-lg border border-grayFifteen bg-white px-4 py-3 text-xl font-semibold text-blackOne">
            {data.title}
          </p>
        ) : (
          <input
            type="text"
            value={editedTitle}
            onChange={e => setEditedTitle(e.target.value)}
            className="mb-4 w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-semibold text-blackOne focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Description */}
      <div>
        {!isEditing ? (
          <div className="prose max-w-none bg-white">
            <p className="whitespace-pre-wrap rounded-lg border border-grayFifteen px-4 py-3 text-gray-700">
              {data.description}
            </p>
          </div>
        ) : (
          <textarea
            value={editedDescription}
            onChange={e => setEditedDescription(e.target.value)}
            rows={6}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Focus Countries */}
      <div className="flex items-center gap-2">
        <div className="mt-2 flex items-center justify-between rounded-xl bg-peachOne">
          <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
            Focus Countries
          </div>
        </div>

        {/* Country Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => {
              if (availableCountries.length > 0) {
                setShowCountryDropdown(!showCountryDropdown);
              }
            }}
            className={`flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 ${
              selectedCountries.length >= 5
                ? 'cursor-not-allowed bg-gray-100'
                : 'bg-peachOne'
            } px-3 py-2 text-left text-sm`}
          >
            <div className="flex flex-wrap gap-2">
              {selectedCountries.map(country => (
                <div
                  key={country.code}
                  className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                >
                  <span className="text-lg">
                    {countryFlags[country.name] || '🏳️'}
                  </span>
                  <span className="text-sm">{country.name}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveCountry(country.name)}
                    className="ml-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-black text-white hover:bg-red-600"
                  >
                    <X size={8} />
                  </button>
                </div>
              ))}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </button>

          {showCountryDropdown && availableCountries.length > 0 && (
            <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-300 bg-white shadow-lg">
              {availableCountries.map(country => (
                <button
                  key={country.countryCode}
                  type="button"
                  onClick={() => handleAddCountry(country.shortName!)}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <span className="text-lg">
                    {countryFlags[country.shortName!] || '🏳️'}
                  </span>
                  <span>{country.shortName}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      {/* Error Message */}
      {countryError && <p className="text-sm text-red-600">{countryError}</p>}

      {/* Goals Categories */}
      <div className="flex items-center gap-2">
        <div className="mt-2 flex items-center justify-between rounded-xl bg-peachOne">
          <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
            Goals Categories
          </div>
        </div>

        {/* Goals Categories Dropdown */}
        <div className="relative" ref={goalsCategoriesDropdownRef}>
          <button
            type="button"
            onClick={() => {
              if (availableGoalsCategories.length > 0) {
                setShowGoalsCategoriesDropdown(!showGoalsCategoriesDropdown);
              }
            }}
            className="flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 bg-peachOne px-3 py-2 text-left text-sm"
          >
            <div className="flex flex-wrap gap-2">
              {selectedGoalsCategories.map(category => (
                <div
                  key={category.categoryRef}
                  className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                >
                  <span className="text-sm">{category.categoryName}</span>
                  <button
                    type="button"
                    onClick={() =>
                      handleRemoveGoalsCategory(category.categoryRef)
                    }
                    className="ml-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-black text-white hover:bg-red-600"
                  >
                    <X size={8} />
                  </button>
                </div>
              ))}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </button>

          {showGoalsCategoriesDropdown &&
            availableGoalsCategories.length > 0 && (
              <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-300 bg-white shadow-lg">
                {availableGoalsCategories.map(category => (
                  <button
                    key={category.categoryRef}
                    type="button"
                    onClick={() => handleAddGoalsCategory(category.categoryRef)}
                    className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-50"
                  >
                    <span>{category.categoryName}</span>
                  </button>
                ))}
              </div>
            )}
        </div>
      </div>
    </div>
  );
}
