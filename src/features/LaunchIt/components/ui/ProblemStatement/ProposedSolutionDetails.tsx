import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, PencilLine, Plus } from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import {
  ProposedSolution,
  useGenerateProposedProjectsApi,
} from '../../../services/launchItApiRequests';
import { ApiError } from '../../../types';
import { useCustomToast } from '@/hooks/useToast';
import { useAppContext } from '@/context/event/AppEventContext';
import solutionThumbnail from '../../../assets/images/solutionThumbnail.png';
import { projectLevels } from '@/features/Categories/data/data';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import ProposedSolutionActionButtons from './ProposedSolutionActionButtons';
import {
  useUpdateProposedSolution,
  useUpdateProposedGoal,
} from '../../../hooks/apiQueryHooks/launchItQueryHooks';
import { Spinner } from '@/components/ui/CommonWidget/Loader';
import Button from '@/components/ui/ButtonComponent';
import { Helper } from '@/utils/helpers';

interface ProjectLevelForm {
  projectLevel: string;
}

export default function ProposedSolutionDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const { solutionRef } = useParams();
  const { currentAccountType } = useAppContext();
  const { successToast, errorToast } = useCustomToast();

  // Edit state management
  const [isEditing, setIsEditing] = useState(false);
  const [editedDescription, setEditedDescription] = useState('');
  const [editedSolutionStatement, setEditedSolutionStatement] = useState('');
  const [editedKeyResults, setEditedKeyResults] = useState<
    Array<{
      referenceId: string;
      keyResultDescription: string;
    }>
  >([]);
  const [newKeyResult, setNewKeyResult] = useState('');
  const [isAddingKeyResult, setIsAddingKeyResult] = useState(false);

  // Local state for current solution to persist updates
  const [localCurrentSolution, setLocalCurrentSolution] =
    useState<ProposedSolution | null>(null);

  // Form setup for project level selection
  const {
    control,
    watch,
    formState: { errors },
  } = useForm<ProjectLevelForm>({
    defaultValues: {
      projectLevel: '',
    },
  });

  const selectedProjectLevel = watch('projectLevel');

  // API setup
  const generateProposedProjectsApi = useGenerateProposedProjectsApi();

  const generateProposedProjectsMutation = useMutation({
    mutationFn: generateProposedProjectsApi,
    onSuccess: response => {
      console.log('Generate Proposed Projects Success:', response);
      successToast('Proposed projects generated successfully!');

      // Navigate to ProposedProjectsScreen with the response data
      navigate(`/${currentAccountType}/launch/proposed-projects`, {
        state: {
          proposedProjects: response.data,
          selectedSolution: currentSolution,
        },
      });
    },
    onError: (error: ApiError) => {
      console.error('Generate Proposed Projects Error:', error);
      errorToast(
        error?.response?.data?.message ||
          'Failed to generate proposed projects. Please try again.',
      );
    },
  });

  // Update API mutations
  const { mutate: updateProposedSolution, isPending: isUpdatingSolution } =
    useUpdateProposedSolution({
      onSuccess: () => {
        successToast('Proposed solution updated successfully!');
        // Update the solution statement in the current solution
        updateCurrentSolutionData({
          solutionStatement: editedSolutionStatement,
        });
      },
      onError: (error: any) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to update solution statement. Please try again.',
        );
      },
    });

  const { mutate: updateProposedGoal, isPending: isUpdatingGoal } =
    useUpdateProposedGoal({
      onSuccess: () => {
        // successToast('Goal and key results updated successfully!');
        // Update the goal data in the current solution
        updateCurrentSolutionData({
          goals: [
            {
              ...currentSolution!.goals[0],
              description: editedDescription,
              goalsKeyResults: editedKeyResults,
            },
          ],
        });
        setIsEditing(false);
      },
      onError: (error: any) => {
        errorToast(
          error?.response?.data?.message ||
            'Failed to update goal and key results. Please try again.',
        );
      },
    });

  const isUpdating = isUpdatingSolution || isUpdatingGoal;

  // Get the proposed solutions and current solution from location state
  const { proposedSolutions } = location.state || {};
  const originalCurrentSolution = proposedSolutions?.find(
    (solution: ProposedSolution) =>
      solution.proposedSolutionRef === solutionRef,
  );

  // Use local state if available, otherwise use original
  const currentSolution = localCurrentSolution || originalCurrentSolution;

  // Function to update current solution data
  const updateCurrentSolutionData = (updates: Partial<ProposedSolution>) => {
    if (currentSolution) {
      setLocalCurrentSolution({
        ...currentSolution,
        ...updates,
      });
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleSaveForLater = () => {
    successToast('Successfully saved for later.');
    navigate(`/${currentAccountType}/dashboard-statements`);
  };

  const handleGenerateProposedProjects = () => {
    if (currentSolution?.proposedSolutionRef && selectedProjectLevel) {
      generateProposedProjectsMutation.mutate({
        proposedSolutionRef: currentSolution.proposedSolutionRef,
        projectLevel: selectedProjectLevel,
      });
    } else if (!selectedProjectLevel) {
      errorToast(
        'Please select a project level before generating proposed projects.',
      );
    }
  };

  // Initialize local state and edit state when original solution changes
  useEffect(() => {
    if (originalCurrentSolution) {
      // Initialize local state if not already set
      if (!localCurrentSolution) {
        setLocalCurrentSolution(originalCurrentSolution);
      }

      // Initialize edit state only if not currently editing
      if (!isEditing) {
        setEditedDescription(currentSolution?.goals[0]?.description || '');
        setEditedSolutionStatement(currentSolution?.solutionStatement || '');
        setEditedKeyResults(currentSolution?.goals[0]?.goalsKeyResults || []);
      }
    }
  }, [
    originalCurrentSolution,
    localCurrentSolution,
    isEditing,
    currentSolution,
  ]);

  // Edit handler functions
  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (!currentSolution) return;

    // Make both API calls
    updateProposedSolution({
      proposedSolutionRef: currentSolution.proposedSolutionRef,
      solutionStatement: editedSolutionStatement,
    });

    updateProposedGoal({
      proposedGoalRef: currentSolution.goals[0]?.proposedGoalRef,
      description: editedDescription,
      goalsKeyResults: editedKeyResults,
    });
  };

  const handleAddKeyResult = () => {
    if (!newKeyResult.trim()) return;

    const newKeyResultObj = {
      referenceId: Helper.getUniqueReference('KR_'),
      keyResultDescription: newKeyResult.trim(),
    };

    setEditedKeyResults(prev => [...prev, newKeyResultObj]);
    setNewKeyResult('');
    setIsAddingKeyResult(false);
  };

  const handleRemoveKeyResult = (referenceId: string) => {
    setEditedKeyResults(prev =>
      prev.filter(kr => kr.referenceId !== referenceId),
    );
  };

  const handleKeyResultChange = (referenceId: string, value: string) => {
    setEditedKeyResults(prev =>
      prev.map(kr =>
        kr.referenceId === referenceId
          ? { ...kr, keyResultDescription: value }
          : kr,
      ),
    );
  };

  const solutionIndex =
    proposedSolutions?.findIndex(
      (solution: ProposedSolution) =>
        solution.proposedSolutionRef === solutionRef,
    ) + 1;

  if (!currentSolution) {
    return (
      <div>
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">Go Back</span>
          </button>
        </div>

        <div className="-mt-48 flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-blackOne">
              Solution not available
            </h2>
            <p className="mt-2 text-gray-600">
              The requested solution could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2 px-3.5">
          <button
            onClick={handleBack}
            className="flex cursor-pointer items-center gap-2 transition-all duration-300 ease-in-out hover:scale-105"
          >
            <ArrowLeft size={24} className="-mt-0.5 text-primary" />
            <span className="text-lg font-medium">
              Proposed Solution #{solutionIndex}
            </span>
          </button>
        </div>

        <div className="-mt-7">
          {/* Action Buttons */}
          <ProposedSolutionActionButtons
            onSaveForLater={handleSaveForLater}
            onGenerateProposedProjects={handleGenerateProposedProjects}
            isGeneratingProposedProjects={
              generateProposedProjectsMutation.isPending
            }
            isUpdating={isUpdating || isEditing}
          />
        </div>
      </div>

      <div className="mx-auto max-w-4xl space-y-6">
        <div className="flex w-full items-center justify-center">
          <img
            src={solutionThumbnail}
            alt="Project Thumbnail"
            className="h-64 w-80 rounded-2xl object-cover"
          />
        </div>

        {/* Proposed Goal & Key Metrics Section */}
        <div className="rounded-lg border border-grayFifteen bg-white shadow-sm">
          <div className="mb-4 flex items-center justify-between rounded-t-lg bg-darkGray p-2">
            <div className="text-xl font-semibold text-white">
              Proposed Goal & Key Metrics
            </div>
            {!isEditing ? (
              <button
                onClick={handleEdit}
                className="flex items-center gap-2 rounded bg-white p-2 hover:bg-gray-200"
              >
                Edit
                <PencilLine size={20} />
              </button>
            ) : (
              <Button
                onClick={handleSave}
                disabled={isUpdating}
                className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isUpdating ? (
                  <div className="flex items-center gap-2">
                    <Spinner className="h-4 w-4 border-primary border-b-[transparent]" />
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save'
                )}
              </Button>
            )}
          </div>

          {/* Description */}
          <div className="px-2">
            {!isEditing ? (
              <div className="text-xl font-bold capitalize text-blackOne">
                {currentSolution.goals[0]?.description || ''}
              </div>
            ) : (
              <input
                type="text"
                value={editedDescription}
                onChange={e => setEditedDescription(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-bold capitalize text-blackOne focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="Enter goal description..."
              />
            )}
          </div>

          {/* Solution statement */}
          <div className="p-2">
            {!isEditing ? (
              <p className="leading-relaxed text-gray-700">
                {currentSolution.solutionStatement}
              </p>
            ) : (
              <textarea
                value={editedSolutionStatement}
                onChange={e => setEditedSolutionStatement(e.target.value)}
                rows={4}
                className="w-full rounded-md border border-gray-300 px-3 py-2 leading-relaxed text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="Enter solution statement..."
              />
            )}
          </div>

          {/* Key results section */}
          <div className="px-2">
            {(isEditing
              ? editedKeyResults
              : currentSolution.goals[0]?.goalsKeyResults || []
            ).map((keyResult: any, index: number) => (
              <div
                key={keyResult.referenceId}
                className="mb-2 rounded-xl border border-gray-200 bg-peachOne p-2"
              >
                <div className="mb-4 flex items-center justify-between">
                  <div className="w-fit rounded border border-gray-200 bg-white px-4 py-1.5 font-medium">
                    Key Result {index + 1}
                  </div>
                  {isEditing && (
                    <button
                      onClick={() =>
                        handleRemoveKeyResult(keyResult.referenceId)
                      }
                      className="text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  )}
                </div>
                {!isEditing ? (
                  <p className="leading-relaxed text-gray-700">
                    {keyResult.keyResultDescription}
                  </p>
                ) : (
                  <textarea
                    value={keyResult.keyResultDescription}
                    onChange={e =>
                      handleKeyResultChange(
                        keyResult.referenceId,
                        e.target.value,
                      )
                    }
                    rows={2}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 leading-relaxed text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    placeholder="Enter key result description..."
                  />
                )}
              </div>
            ))}

            {/* Add New Key Result */}
            {isEditing && (
              <div className="mb-2 rounded-xl border border-gray-200 bg-peachOne p-2">
                {!isAddingKeyResult ? (
                  <button
                    onClick={() => setIsAddingKeyResult(true)}
                    className="flex items-center gap-2 rounded border border-gray-200 bg-white px-4 py-1.5 font-medium hover:bg-grayNineTeen"
                  >
                    Add
                    <Plus size={20} className="-mt-1" />
                  </button>
                ) : (
                  <div className="space-y-2">
                    <div className="w-fit rounded border border-gray-200 bg-white px-4 py-1.5 font-medium">
                      New Key Result
                    </div>
                    <textarea
                      value={newKeyResult}
                      onChange={e => setNewKeyResult(e.target.value)}
                      rows={2}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 leading-relaxed text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      placeholder="Enter new key result description..."
                      autoFocus
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={handleAddKeyResult}
                        disabled={!newKeyResult.trim()}
                        className="rounded bg-primary px-4 py-2 text-white hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        Save
                      </Button>
                      <Button
                        onClick={() => {
                          setIsAddingKeyResult(false);
                          setNewKeyResult('');
                        }}
                        className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-gray-300"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Project Level Selection */}
        <div className="mt-8 rounded-lg border border-gray-200 bg-white p-6 pt-4">
          <p className="mb-2 text-lg font-semibold text-blackOne">
            Project Level
          </p>
          <FormSelectBox<ProjectLevelForm>
            labelName="Choose the minimum project level required for the proposed projects"
            options={projectLevels}
            optionsArr={projectLevels}
            control={control}
            name="projectLevel"
            errors={errors}
            placeholder="Select Project Level"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end">
          <ProposedSolutionActionButtons
            onSaveForLater={handleSaveForLater}
            onGenerateProposedProjects={handleGenerateProposedProjects}
            isGeneratingProposedProjects={
              generateProposedProjectsMutation.isPending
            }
            isUpdating={isUpdating || isEditing}
          />
        </div>
      </div>
    </div>
  );
}
