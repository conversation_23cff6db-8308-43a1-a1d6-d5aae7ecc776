import classNames from 'classnames';

type Props = {
  replyTo: {
    _id: string;
    content: string;
    senderName: string;
  };
  isSender: boolean;
  onReplyClick?: () => void;
};

export const ReplyToMessage = ({ replyTo, isSender, onReplyClick }: Props) => {
  return (
    <div
      className={classNames(
        'flex cursor-pointer items-center gap-2 rounded-2xl rounded-b-xl rounded-tr-none p-2 text-[12px] transition-colors hover:bg-opacity-80',
        isSender ? 'bg-blue-50' : 'bg-gray-100',
      )}
      onClick={onReplyClick}
    >
      <span
        className={classNames(
          'h-9 w-1 shrink-0 rounded-full',
          isSender ? 'bg-blue-500' : 'bg-orange-500',
        )}
      />
      <div className="flex flex-col items-start gap-1">
        <p
          className={classNames(
            'text-[11px] font-semibold',
            isSender ? 'text-blue-600' : 'text-orange-600',
          )}
        >
          {isSender ? 'You' : replyTo?.senderName}
        </p>
        <p className="line-clamp-2 text-[11px] leading-tight text-gray-700">
          {replyTo?.content || 'No content'}
        </p>
      </div>
    </div>
  );
};
