import { X } from 'lucide-react';
import { useChatContext } from '../../../context/chat/ChatContext';

export const ReplyPreview = () => {
  const { replyingTo, setReplyingToHandler } = useChatContext();

  if (!replyingTo) return null;

  return (
    <div className="mx-auto w-full max-w-[95%] rounded-t-[16px] border-l-4 border-orange-500 bg-gray-100 p-3">
      <div className="flex items-center justify-between">
        <div className="min-w-0 flex-1">
          <p className="mb-1 text-[11px] font-semibold text-orange-600">
            Replying to {replyingTo.senderName}
          </p>
          <p className="line-clamp-2 text-[11px] leading-tight text-gray-700">
            {replyingTo.content}
          </p>
        </div>
        <button
          onClick={() => setReplyingToHandler(null)}
          className="ml-2 flex-shrink-0 rounded-full p-1 transition-colors hover:bg-gray-200"
        >
          <X className="h-4 w-4 text-gray-500" />
        </button>
      </div>
    </div>
  );
};
