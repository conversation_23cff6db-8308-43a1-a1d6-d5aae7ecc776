import moment, { MomentInput } from 'moment';
import { Fragment } from 'react';

import { profilePlaceholder } from '@/assets/images';

import DocChatBubble from './DocChatBubble';
import PdfChatBubble from './PdfChatBubble';
import ImageChatBubble from './ImageChatBubble';
import TextAndLinkChatBubble from './TextAndLinkChatBubble';

import { ChatHelper } from '@/features/Chat/utils/helper';
import { file, LinkMetadata } from '@/types';
import { ChatOpenGraphComponent } from '@/components/ui/CommonWidget/OpenGraphComponent';
import VideoChatBubble from './VideoChatBubble';

export const ChatBubble = ({
  message = '',
  edited,
  isSender = false,
  dateSent,
  dateUpdated,
  fileObjects = [],
  senderImage,
  senderName,
  messageId,
  linkMetadata,
  messageRepliedTo,
  onScrollToMessage,
}: {
  edited: boolean;
  message?: string;
  isSender?: boolean;
  dateSent: MomentInput;
  dateUpdated: MomentInput;
  files?: string[];
  fileObjects: file[];
  senderImage?: string;
  senderName?: string;
  messageId: string;
  linkMetadata: LinkMetadata | null;
  messageRepliedTo?:
    | {
        _id: string;
        content: string;
        senderName: string;
      }
    | string
    | null;
  onScrollToMessage?: (messageId: string) => void;
}) => {
  return (
    <div
      id={`message-${messageId}`}
      className={`mt-4 flex w-full flex-col gap-[5px] transition-colors ${
        isSender ? 'items-end' : 'items-start'
      }`}
    >
      <div className="flex max-w-[80%] gap-1">
        {!isSender && (
          <div className="h-10 w-10 flex-shrink-0">
            <img
              loading="lazy"
              src={senderImage || profilePlaceholder}
              alt="profile"
              className={`h-full w-full rounded-full object-cover transition duration-300 ease-in-out ${'opacity-100'}`}
            />
          </div>
        )}
        <div className="w-full">
          <p
            className={`mb-2 text-[12px] font-bold text-black ${isSender ? 'text-right' : 'text-left'}`}
          >
            {senderName || `Anonymous`}
          </p>
          {fileObjects && fileObjects.length > 0 && (
            <>
              {fileObjects?.map(({ fileUrl }, index) => (
                <Fragment key={fileUrl + index}>
                  {ChatHelper.isDoc(fileUrl) && (
                    <DocChatBubble
                      isSender={isSender}
                      key={fileUrl}
                      file={fileUrl}
                    />
                  )}

                  {ChatHelper.isPdf(fileUrl) && (
                    <PdfChatBubble
                      isSender={isSender}
                      key={fileUrl}
                      file={fileUrl}
                    />
                  )}

                  {ChatHelper.isImage(fileUrl) && (
                    <ImageChatBubble
                      isSender={isSender}
                      key={index}
                      url={fileUrl}
                    />
                  )}
                  {ChatHelper.isVideo(fileUrl) && (
                    <VideoChatBubble
                      isSender={isSender}
                      key={index}
                      url={fileUrl}
                    />
                  )}
                </Fragment>
              ))}
            </>
          )}
          {/* {messageRepliedTo && typeof messageRepliedTo === 'object' && (
            <ReplyToMessage
              replyTo={messageRepliedTo}
              isSender={isSender}
              onReplyClick={() => onScrollToMessage?.(messageRepliedTo._id)}
            />
          )} */}
          {linkMetadata && (
            <ChatOpenGraphComponent {...linkMetadata} isSender={isSender} />
          )}
          {message && (
            <TextAndLinkChatBubble
              edited={edited}
              isSender={isSender}
              message={message}
              messageId={messageId}
              dateSent={dateSent}
              dateUpdated={dateUpdated}
              senderName={senderName || 'Unknown'}
              messageRepliedTo={
                typeof messageRepliedTo === 'object' &&
                messageRepliedTo !== null
                  ? messageRepliedTo
                  : null
              }
              onScrollToMessage={onScrollToMessage ?? (() => {})}
            />
          )}
        </div>

        {isSender && (
          <div className="h-10 w-10 flex-shrink-0">
            <img
              loading="lazy"
              src={senderImage || profilePlaceholder}
              alt="profile"
              className={`h-full w-full rounded-full object-cover transition duration-300 ease-in-out ${'opacity-100'}`}
            />
          </div>
        )}
      </div>
      <p className="text-[12px] text-grayNine">
        {moment(dateSent).format('h:mm a')}
      </p>
    </div>
  );
};
