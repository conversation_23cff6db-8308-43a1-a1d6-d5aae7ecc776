import { ElementRef, Fragment, useRef } from 'react';

import { ChatBubble } from './ChatBubble';
import { FileUpload } from './FileUpload';
import { Spinner } from '../../../../../components/ui/CommonWidget/Loader';
import { useChatQuery } from '../../../hooks/useChatQuery';
import { useChatScroll } from '../../../hooks/useChatScroll';
import { useUserContext } from '../../../../../context/user/UserContext';
import { useChatContext } from '@/features/Chat/context/chat/ChatContext';
import { useGetChatMessages } from '../../../hooks/apiQueryHooks/chatQueryHooks';

import { ChatHelper } from '../../../utils/helper';
import { chatEvents } from '../../../data/constant';

type ChatMessageProps = {
  chatRoomId: string;
  queryKey: string;
};
export const MessageBoxContainer = ({
  chatRoomId,
  queryKey,
}: ChatMessageProps) => {
  const { data: user } = useUserContext();
  const { showDropzone } = useChatContext();

  const chatRef = useRef<ElementRef<'div'>>(null);
  const bottomRef = useRef<ElementRef<'div'>>(null);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, status } =
    useGetChatMessages(
      {
        queryKey,
        chatRoomId,
      },
      { enabled: !!chatRoomId },
    );

  useChatScroll({
    chatRef,
    bottomRef,
    loadMore: fetchNextPage,
    shouldLoadMore: !isFetchingNextPage && !!hasNextPage,
    count: data?.pages?.[0].data.length ?? 0,
  });

  useChatQuery({ key: chatEvents.NEW_CHAT_MESSAGE });

  if (!chatRoomId)
    return (
      <div className="flex h-auto flex-1 flex-grow  flex-col items-center justify-center gap-4 overflow-auto bg-[#F7C1931A] px-9 py-6">
        <p className="text-xs text-zinc-500 dark:text-zinc-400">
          Select a chat to see conversations
        </p>
      </div>
    );
  if (status === 'loading') {
    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <Spinner />
        <p className="text-xs text-zinc-500 dark:text-zinc-400">
          Loading messages...
        </p>
      </div>
    );
  }
  if (status === 'error') {
    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <p className="text-xs text-zinc-500 dark:text-zinc-400">
          Something went wrong!
        </p>
      </div>
    );
  }
  return (
    <>
      {showDropzone ? (
        <>
          <div className="flex h-auto max-w-full flex-1 flex-col items-center justify-center gap-4 overflow-auto px-9 py-6">
            <FileUpload />
          </div>
        </>
      ) : (
        <>
          {data?.pages[0]?.data?.length > 0 ? (
            <div
              ref={chatRef}
              className="flex h-auto max-w-full flex-1 flex-col gap-4 overflow-auto px-3 py-6 xs:px-9"
            >
              {!hasNextPage && <div className="flex-1" />}
              {hasNextPage && (
                <div className="flex justify-center">
                  {isFetchingNextPage ? (
                    <Spinner />
                  ) : (
                    <button
                      onClick={() => fetchNextPage()}
                      className="my-4 text-xs text-zinc-500 transition hover:text-zinc-600 dark:text-zinc-400 dark:hover:text-zinc-300"
                    >
                      Load previous messages
                    </button>
                  )}
                </div>
              )}
              <div className="mt-auto flex flex-col-reverse">
                {data?.pages?.map((group, index) => (
                  <Fragment key={index}>
                    {Object.entries(
                      ChatHelper.groupChatMessagesByDay(group.data),
                    ).map(([key, value]) => {
                      return (
                        <div key={key}>
                          <p className="text-center text-[12px] text-black">
                            {key}
                          </p>
                          <div className="flex flex-col-reverse">
                            <Fragment>
                              {value?.map((message, index) => {
                                // Find the referenced message if messageRepliedTo is a string ID
                                let resolvedMessageRepliedTo =
                                  message?.messageRepliedTo;
                                if (
                                  typeof message?.messageRepliedTo ===
                                    'string' &&
                                  data?.pages
                                ) {
                                  // Search through all pages for the referenced message
                                  for (const page of data.pages) {
                                    const referencedMessage = page.data?.find(
                                      (msg: any) =>
                                        msg._id === message.messageRepliedTo,
                                    );
                                    if (referencedMessage) {
                                      resolvedMessageRepliedTo = {
                                        ...referencedMessage,
                                        _id: referencedMessage._id,
                                        content: referencedMessage.content,
                                        senderName:
                                          referencedMessage.senderName,
                                        senderId: referencedMessage.senderId,
                                      };
                                      break;
                                    }
                                  }
                                }

                                const scrollToMessage = (
                                  targetMessageId: string,
                                ) => {
                                  const element = document.getElementById(
                                    `message-${targetMessageId}`,
                                  );
                                  if (element) {
                                    element.scrollIntoView({
                                      behavior: 'smooth',
                                      block: 'center',
                                    });
                                    // Add a highlight effect
                                    element.classList.add('bg-yellow-100');
                                    setTimeout(() => {
                                      element.classList.remove('bg-yellow-100');
                                    }, 2000);
                                  }
                                };

                                return (
                                  <ChatBubble
                                    key={index + message?.createdAt}
                                    messageId={message?._id}
                                    edited={message?.edited}
                                    senderName={message?.senderName}
                                    senderImage={message?.senderImage}
                                    dateSent={message?.createdAt}
                                    dateUpdated={message?.updatedAt}
                                    message={message?.content}
                                    fileObjects={message?.fileObjects}
                                    linkMetadata={message?.linkMetadata}
                                    messageRepliedTo={resolvedMessageRepliedTo}
                                    onScrollToMessage={scrollToMessage}
                                    isSender={
                                      user?.user?.userId === message?.senderId
                                    }
                                  />
                                );
                              })}
                            </Fragment>
                          </div>
                        </div>
                      );
                    })}
                  </Fragment>
                ))}
              </div>
              <div ref={bottomRef} />
            </div>
          ) : (
            <div className="flex h-auto flex-1 flex-grow flex-col items-center justify-center gap-4 overflow-auto px-9 py-6">
              <p className="text-xs text-zinc-500 dark:text-zinc-400">
                Let's get started
              </p>
            </div>
          )}
        </>
      )}
    </>
  );
};
