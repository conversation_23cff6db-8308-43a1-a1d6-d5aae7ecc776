import { useRef } from 'react';

import { ArrowBackIcon } from '../../../../../assets/icons';
import { useHandleQueryParams } from '../../../../../hooks/useHandleQueryParams';
import { useOnClickOutside } from '../../../../../hooks/useOnClickOutside';
import { useChatContext } from '../../../context/chat/ChatContext';
import { ChatInputBox } from '../../forms/ChatInputBox';
import { ChatBoxHeader } from './ChatBoxHeader';
import { ChatThreadContainer } from './ChatThreadContainer';
import ContactThreadContainer from './ContactThreadContainer';
import { MessageBoxContainer } from './MessageBoxContainer';
import NewChatOptions from './NewChatOptions';
import StartNewChat from './StartNewChat';
import bgImage from '../../../assets/images/chat-bg.png';

const ChatContainer = () => {
  const chatOptionsRef = useRef<HTMLDivElement>(null);
  const { query, handleQuery } = useHandleQueryParams();
  const chatRoomId = query.get('chat_room_id');
  const queryKey = `chat:${chatRoomId}`;

  const { newChatOptions, showNewChatOptionsHandler } = useChatContext();

  useOnClickOutside(chatOptionsRef, (e?: MouseEvent | TouchEvent) => {
    const target = e?.target as Element;
    const chat = document.querySelector('#start-chat');

    if ((target?.id && target.id === 'start-chat') || chat?.contains(target))
      return;
    showNewChatOptionsHandler(false);
  });

  return (
    <div className="flex h-[90vh] overflow-y-hidden rounded-2xl bg-white pt-8">
      <div
        className={`${
          chatRoomId ? 'hidden' : 'block'
        } relative h-full flex-[30%] flex-shrink-0 border-r border-r-grayTwo md:block`}
      >
        <StartNewChat />
        {query.get('new_chat') ? (
          <ContactThreadContainer />
        ) : (
          <ChatThreadContainer />
        )}
        {newChatOptions && (
          <div
            ref={chatOptionsRef}
            className="absolute left-0 right-0 top-[50px] z-[5]"
          >
            <NewChatOptions />
          </div>
        )}
      </div>
      <div
        className={`${
          chatRoomId ? 'flex' : 'hidden'
        } h-full w-full max-w-[70%] flex-col max-md:max-w-full md:flex md:w-auto md:flex-[70%]`}
      >
        <div className="mb-3 block px-6 md:hidden">
          <ArrowBackIcon
            className="cursor-pointer"
            onClick={() => handleQuery({ chat_room_id: undefined })}
          />
        </div>
        <ChatBoxHeader />
        <div
          style={{ backgroundImage: `url(${bgImage})` }}
          className={`flex h-[86%] flex-col bg-cover`}
        >
          <MessageBoxContainer
            queryKey={queryKey}
            chatRoomId={chatRoomId || ''}
          />
          {chatRoomId && <ChatInputBox chatRoomId={chatRoomId || ''} />}
        </div>
      </div>
    </div>
  );
};

export default ChatContainer;
