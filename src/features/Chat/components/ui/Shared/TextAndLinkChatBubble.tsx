import parse from 'html-react-parser';
import moment, { MomentInput } from 'moment';

import { PencilIcon } from '@/features/Profile/assets';

import { useCustomMedia } from '@/hooks/useCustomMedia';
import { Helper } from '@/utils/helpers';
import { useAppContext } from '@/context/event/AppEventContext';
import { useHandleQueryParams } from '@/hooks/useHandleQueryParams';
import { useState } from 'react';
import { useChatContext } from '../../../context/chat/ChatContext';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { MoreVerticalIcon, ReplyIcon } from 'lucide-react';
import { ReplyToMessage } from './ReplyToMessage';
import classNames from 'classnames';

type Props = {
  edited: boolean;
  message: string;
  isSender: boolean;
  messageId: string;
  dateSent: MomentInput;
  dateUpdated: MomentInput;
  senderName: string;
  messageRepliedTo: {
    _id: string;
    content: string;
    senderName: string;
  } | null;
  onScrollToMessage: (messageId: string) => void;
};

export default function TextAndLinkChatBubble({
  edited,
  message,
  isSender,
  messageId,
  dateSent,
  dateUpdated,
  senderName,
  messageRepliedTo,
  onScrollToMessage,
}: Props) {
  const { handleQuery } = useHandleQueryParams();
  const { setShowModalHandler } = useAppContext();
  const { screenSize } = useCustomMedia();
  const { setReplyingToHandler } = useChatContext();
  const [allDescription, showAllDescription] = useState(false);
  const minutesPassed = Helper.getTimeDifferenceInMinutes(dateSent, undefined);
  const [showPopover, setShowPopover] = useState(false);
  return (
    <div
      className={classNames(
        'group relative flex w-full flex-1 flex-col gap-2.5 rounded-2xl text-xs font-semibold [&_a]:w-full [&_a]:cursor-pointer',
        isSender ? '[&_a]:text-black' : '[&_a]:text-primary',
        '[&_a]:underline',
        isSender
          ? '!rounded-tr-none bg-[#A2E0FF]'
          : '!rounded-tl-none bg-white',
        messageRepliedTo && typeof messageRepliedTo === 'object'
          ? 'p-1 pb-3'
          : 'p-3',
      )}
    >
      {messageRepliedTo && typeof messageRepliedTo === 'object' && (
        <ReplyToMessage
          replyTo={messageRepliedTo}
          isSender={isSender}
          onReplyClick={() => onScrollToMessage?.(messageRepliedTo._id)}
        />
      )}
      <div className="px-2 text-xs">
        {allDescription ? (
          <>
            {parse(Helper.wrapLongWordsWithBreakAll(Helper.linkify(message)))}

            <span
              onClick={() => showAllDescription(false)}
              className="cursor-pointer text-xs font-semibold text-primary"
            >
              See less
            </span>
          </>
        ) : (
          <>
            {message && message?.length > 200 ? (
              <>
                {parse(
                  Helper.wrapLongWordsWithBreakAll(
                    Helper.linkify(message?.slice(0, 200)),
                  ),
                )}
                ...
                <span
                  onClick={() => showAllDescription(true)}
                  className="cursor-pointer text-[12px] font-semibold text-primary"
                >
                  See more
                </span>
              </>
            ) : (
              <>
                {parse(
                  Helper.wrapLongWordsWithBreakAll(Helper.linkify(message)),
                )}
              </>
            )}
          </>
        )}
      </div>
      {edited && (
        <p
          className={`mb-0 mt-2 text-[8px] font-bold ${isSender ? 'text-right' : 'text-left'}`}
        >
          Edited {moment(dateUpdated).format('h:mma')}
        </p>
      )}

      <div
        className={classNames(
          'absolute flex cursor-pointer items-center transition-all duration-150',
          screenSize > 768
            ? 'right-0 top-0 w-0 !rounded-bl-[16px] bg-primary p-2 opacity-0 backdrop-blur-md group-hover:w-8 group-hover:opacity-100'
            : '-right-4 bottom-0 w-4',
          showPopover && 'right-0 w-8 opacity-100',
        )}
      >
        <Popover open={showPopover} onOpenChange={setShowPopover}>
          <PopoverTrigger asChild>
            <div className="flex items-center justify-center">
              <MoreVerticalIcon
                className="h-4 w-4"
                strokeWidth={1.5}
                stroke="white"
              />
            </div>
          </PopoverTrigger>
          <PopoverContent
            align="end"
            className="w-32 rounded-xl bg-white px-0 py-2"
          >
            <div className="flex flex-col">
              <button
                onClick={() => {
                  setReplyingToHandler({
                    messageId,
                    content: message,
                    senderName,
                  });
                  setShowPopover(false);
                }}
                className="flex w-full items-center rounded-none px-3 py-2 text-left text-[12px] outline-none transition-colors hover:bg-gray-100"
              >
                <ReplyIcon className="mr-2 h-3 w-3" />
                Reply
              </button>
              {isSender && minutesPassed < 5 && (
                <button
                  onClick={() => {
                    setShowModalHandler('EditChatModal');
                    handleQuery({ message_id: messageId, msg: message });
                    setShowPopover(false);
                  }}
                  className="flex w-full items-center rounded-none px-3 py-2 text-left text-[12px] outline-none transition-colors hover:bg-gray-100"
                >
                  <PencilIcon className="mr-2 h-3 w-3" />
                  Edit
                </button>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
