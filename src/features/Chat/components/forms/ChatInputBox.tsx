import { Submit<PERSON>and<PERSON> } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import Form from '../../../../components/forms/Form';
import { useFormDataAndApiMutateHandler } from '../../../../hooks/useFormDataAndApiMutateHandler';
import { Helper } from '../../../../utils/helpers';
import { AttachIcon, SendIcon } from '../../assets/icons';
import { useChatContext } from '../../context/chat/ChatContext';
import { useAddChatMessage } from '../../hooks/apiQueryHooks/chatQueryHooks';
import { addMessageSchema } from '../../lib/yup/validation';
import { IAddMessage } from '../../types';
import useStimulatedFeedback from '@/hooks/useStimulatedFeedback';
import { DialogWithTooltip } from '@/components/ui/CommonWidget/DialogWithTooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { DialogButtonWithTooltip } from '@/components/ui/CommonWidget/DialogButtonWithTooltip';
import { EmojiIcon } from '@/features/Conversation/assets/icons';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { TooltipProvider } from '@/components/ui/tooltip';
import FormTextArea from '@/components/forms/FormTextArea';
import { cn } from '@/lib/twMerge/cn';
import useHandleFetchOpenGraphMetadataFromUrl from '@/features/Conversation/hooks/useHandleFetchOpenGraphMetadataFromUrl';
import { ChatOpenGraphPreviewComponent } from '@/components/ui/CommonWidget/OpenGraphPreviewComponent';
import { ReplyPreview } from '../ui/Shared/ReplyPreview';

export const ChatInputBox = ({ chatRoomId }: { chatRoomId: string }) => {
  const [url, setUrl] = useState('');
  const [rowSize, setRowSize] = useState(1);
  const progressRef = useRef<NodeJS.Timeout>();
  const { startSimulatedProgress, setUploadProgressHandler } =
    useStimulatedFeedback();
  const {
    setShowDropzoneHandler,
    files,
    setFilesHandler,
    setReplyingToHandler,
    replyingTo,
  } = useChatContext();
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const buttonRef = useRef<HTMLButtonElement | null>(null);

  const next = () => {
    resetField('content');
    progressRef.current && clearInterval(progressRef.current);
    setUploadProgressHandler(100);

    setTimeout(() => {
      setShowDropzoneHandler(false);
      setFilesHandler([]);
      setUploadProgressHandler(0);
    }, 1200);
    setUrl('');
    setShowOpenGraphData(false);
    setReplyingToHandler(null);
  };

  const {
    isLoading,
    resetField,
    register,
    handleSubmit,
    mutate,
    getValues,
    setValue,
    watch,
  } = useFormDataAndApiMutateHandler<IAddMessage, any>(
    addMessageSchema,
    useAddChatMessage,
    { next, noSuccessToast: true },
  );

  const watchedContent = watch('content');

  const {
    data: openGraphUrlData,
    isLoadingGraphUrlData,
    showOpenGraphData,
    setShowOpenGraphData,
  } = useHandleFetchOpenGraphMetadataFromUrl(watchedContent || '', setUrl);

  const onSubmit: SubmitHandler<IAddMessage> = data => {
    if (!files[0] && !data.content) return alert("Can't send empty message");
    progressRef.current = startSimulatedProgress();
    const newData = {
      ...data,
      chatRoomId,
      files: files[0],
      url: showOpenGraphData ? url : '',
      messageRepliedToId: replyingTo?.messageId || null,
    };
    mutate(newData);
  };

  const content = watch('content');

  // Auto-resize textarea on change
  const resizeTextarea = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    resizeTextarea();
  }, [content]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift + Enter -> Allow newline
        return;
      }
      // Prevent default Enter behavior (submit instead)
      event.preventDefault();
      if (buttonRef.current) {
        buttonRef.current.click();
      }
    }
  };

  const addEmoji = (emoji: string) => {
    setValue('content', `${content}${emoji}`, { shouldValidate: true });
  };

  return (
    <>
      {!isLoadingGraphUrlData &&
      !!openGraphUrlData?.data &&
      showOpenGraphData ? (
        <ChatOpenGraphPreviewComponent
          setShowOpenGraphData={setShowOpenGraphData}
          {...openGraphUrlData?.data}
        />
      ) : null}
      <ReplyPreview />
      <TooltipProvider>
        <div>
          <Form
            className="mx-auto w-full max-w-[95%] rounded-[16px]"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="relative">
              <FormTextArea
                rows={rowSize}
                onFocus={() => setRowSize(2)}
                disabled={!chatRoomId}
                ref={textareaRef}
                onKeyDown={handleKeyDown}
                placeholder="Send message"
                registerHanlder={() => register('content')}
                className={cn(
                  `${
                    getValues('content') ===
                    Helper.linkify(getValues('content') ?? '')
                      ? 'text-blackFive'
                      : 'text-primary'
                  } input h-auto w-full flex-1 resize-none overflow-y-hidden rounded-[16px] py-4 pr-[100px] text-[14px]`,
                )}
              />
              <div className="absolute bottom-[25px] right-4 flex items-center gap-x-[6px]">
                <DialogWithTooltip
                  Icon={
                    <AttachIcon
                      onClick={() => {
                        setFilesHandler([]);
                        setShowDropzoneHandler(prev => !prev);
                      }}
                      className="h-5 w-5 cursor-pointer"
                    />
                  }
                  title="Add files"
                />
                <Popover>
                  <PopoverTrigger>
                    <div className="flex">
                      <DialogButtonWithTooltip
                        Icon={<EmojiIcon className="h-5 w-5 cursor-pointer" />}
                        title="Add emoji"
                      />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    align="center"
                    side="top"
                    className="w-fit p-0"
                  >
                    <Picker
                      previewPosition="none"
                      data={data}
                      emojiSize={24}
                      onEmojiSelect={(emoji: { native: string }) =>
                        addEmoji(emoji.native)
                      }
                    />
                  </PopoverContent>
                </Popover>
                <DialogWithTooltip
                  Icon={
                    <button
                      className="flex"
                      ref={buttonRef}
                      disabled={isLoading}
                      type="submit"
                    >
                      <SendIcon className="h-5 w-5 cursor-pointer" />
                    </button>
                  }
                  title="Send"
                />
              </div>
            </div>
          </Form>
        </div>
      </TooltipProvider>
    </>
  );
};
