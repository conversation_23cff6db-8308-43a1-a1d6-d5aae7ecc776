import { SetStateAction, useCallback, useState } from 'react';

import { MessageType } from '../../../../types';

type ReplyingToMessage = {
  messageId: string;
  content: string;
  senderName: string;
} | null;

export default function useChatContextStateAndActions() {
  const [messageType, setMessageType] = useState<MessageType>('All');
  const [newChatOptions, showNewChatOptions] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [showDropzone, setShowDropzone] = useState(false);
  const [replyingTo, setReplyingTo] = useState<ReplyingToMessage>(null);

  const setMessageTypeHandler = useCallback(
    (type: MessageType) => setMessageType(type),
    [],
  );
  const showNewChatOptionsHandler = useCallback(
    (type: SetStateAction<boolean>) => showNewChatOptions(type),
    [],
  );
  const setShowDropzoneHandler = useCallback(
    (type: SetStateAction<boolean>) => setShowDropzone(type),
    [],
  );
  const setFilesHandler = useCallback(
    (value: React.SetStateAction<File[]>) => setFiles(value),
    [],
  );
  const setReplyingToHandler = useCallback(
    (value: React.SetStateAction<ReplyingToMessage>) => setReplyingTo(value),
    [],
  );

  return {
    messageType,
    setMessageTypeHandler,
    newChatOptions,
    showNewChatOptionsHandler,
    setFilesHandler,
    files,
    showDropzone,
    setShowDropzoneHandler,
    replyingTo,
    setReplyingToHandler,
  };
}
