export const GET_PROBLEM_STATEMENT_QUERY = 'GET_PROBLEM_STATEMENT_QUERY';
export const GET_CATEGORY_QUERY = 'GET_CATEGORY_QUERY';
export const GET_PROBLEM_STATEMENTS_QUERY = 'GET_PROBLEM_STATEMENTS_QUERY';
export const REPLY_QUERY = 'REPLY_QUERY';
export const REPLY_COMMENT = 'REPLY_COMMENT';
export const GET_PROJECTS_QUERY = 'GET_PROJECTS_QUERY';
export const GET_PROJECTS_INFINITE_QUERY = 'GET_PROJECTS_INFINITE_QUERY';
export const GET_EXTERNAL_PROJECTS_INFINITE_QUERY =
  'GET_EXTERNAL_PROJECTS_INFINITE_QUERY';
export const GET_AVAILABLE_PROJECTS_INFINITE_QUERY =
  'GET_AVAILABLE_PROJECTS_INFINITE_QUERY';
export const GET_PROJECTS_USER_MEMBEROF_INFINITE_QUERY =
  'GET_PROJECTS_USER_MEMBEROF_INFINITE_QUERY';
export const GET_PROJECTS_TEAMREF_INFINITE_QUERY =
  'GET_PROJECTS_TEAMREF_INFINITE_QUERY';
export const GET_ALL_PROJECTS_QUERY = 'GET_ALL_PROJECTS_QUERY';
export const GET_AVAILABLE_PROJECTS_QUERY = 'GET_AVAILABLE_PROJECTS_QUERY';
export const GET_EXTERNAL_PROJECTS_QUERY = 'GET_EXTERNAL_PROJECTS_QUERY';
export const GET_USER_QUERY = 'GET_USER_QUERY';
export const GET_USER_BY_USERID_QUERY = 'GET_USER_BY_USERID_QUERY';
export const GET_PROJECT_QUERY = 'GET_PROJECT_QUERY';
export const GET_MYTEAMS_QUERY = 'GET_MYTEAMS_QUERY';
export const GET_MYTEAMS_INFINITE_QUERY = 'GET_MYTEAMS_INFINITE_QUERY';
export const GET_AVAILABLE_TEAMS_INFINITE_QUERY =
  'GET_AVAILABLE_TEAMS_INFINITE_QUERY';
export const GET_AVAILABLE_TEAMS_QUERY = 'GET_AVAILABLE_TEAMS_QUERY';
export const GET_PROJECT_TEAMS_QUERY = 'GET_PROJECT_TEAMS_QUERY';
export const GET_TEAMS_CREATEDBY_USER_QUERY = 'GET_TEAMS_CREATEDBY_USER_QUERY';
export const GET_TEAM_QUERY = 'GET_TEAM_QUERY';
export const GET_PROJECT_MEMBERS_QUERY = 'GET_PROJECT_MEMBERS_QUERY';
export const GET_PROJECT_ADMINS_QUERY = 'GET_PROJECT_ADMINS_QUERY';
export const GET_TEAM_MEMBERS_QUERY = 'GET_TEAM_MEMBERS_QUERY';
export const ADD_PROJECT_QUERY = 'ADD_PROJECT_QUERY';
export const ADD_TEAM_QUERY = 'ADD_TEAM_QUERY';
export const UPDATE_TEAM_QUERY = 'UPDATE_TEAM_QUERY';
export const UPDATE_TEAM_VISIBILITY_QUERY = 'UPDATE_TEAM_VISIBILITY_QUERY';
export const DELETE_TEAM_MEMBER_QUERY = 'DELETE_TEAM_MEMBER_QUERY';
export const ADD_GOAL_QUERY = 'ADD_GOAL_QUERY';
export const UPDATE_GOAL_QUERY = 'UPDATE_GOAL_QUERY';
export const DELETE_GOAL_QUERY = 'DELETE_GOAL_QUERY';
export const ADD_PROJECT_TO_GOAL_QUERY = 'ADD_PROJECT_TO_GOAL_QUERY';
export const REMOVE_PROJECT_TO_GOAL_QUERY = 'REMOVE_PROJECT_TO_GOAL_QUERY';
export const GET_GOAL_QUERY = 'GET_GOAL_QUERY';
export const GET_GOAL_DETAILS_QUERY = 'GET_GOAL_DETAILS_QUERY';
export const GET_GOAL_TRAFFIC_QUERY = 'GET_GOAL_TRAFFIC_QUERY';
export const GET_PROJECTS_TASKS_QUERY = 'GET_PROJECTS_TASKS_QUERY';
export const GET_MY_GOALS_QUERY = 'GET_MY_GOALS_QUERY';
export const GET_USER_CREATED_GOALS_QUERY = 'GET_USER_CREATED_GOALS_QUERY';
export const ADD_TASK_QUERY = 'ADD_TASK_QUERY';
export const MOVE_TASK_QUERY = 'MOVE_TASK_QUERY';
export const MOVE_OBJECTIVE_TO_PROJECT_QUERY =
  'MOVE_OBJECTIVE_TO_PROJECT_QUERY';
export const UPDATE_TASK_PROGRESS_QUERY = 'UPDATE_TASK_PROGRESS_QUERY';
export const UPDATE_TASK_QUERY = 'UPDATE_TASK_QUERY';
export const GET_TASK_QUERY = 'GET_TASK_QUERY';
export const DELETE_TASK_QUERY = 'DELETE_TASK_QUERY';
export const DUPLICATE_TASK_QUERY = 'DUPLICATE_TASK_QUERY';
export const DELETE_TASK_ATTACHMENT_QUERY = 'DELETE_TASK_ATTACHMENT_QUERY';

export const REQUEST_TO_JOIN_TEAM_QUERY = 'REQUEST_TO_JOIN_TEAM_QUERY';
export const REQUEST_TO_JOIN_PROJECT_QUERY = 'REQUEST_TO_JOIN_PROJECT_QUERY';
export const ADD_PROBLEM_STATEMENT_QUERY = 'ADD_PROBLEM_STATEMENT_QUERY';
export const PROCESS_REQUEST_TO_JOIN_PROJECT_QUERY =
  'PROCESS_REQUEST_TO_JOIN_PROJECT_QUERY';
export const PROCESS_USER_INVITE_QUERY = 'PROCESS_USER_INVITE_QUERY';
export const PROCESS_USER_REQUEST_TO_JOIN_QUERY =
  'PROCESS_USER_REQUEST_TO_JOIN_QUERY';
export const ADD_TEAM_TO_PROJECT_QUERY = 'ADD_TEAM_TO_PROJECT_QUERY';
export const REMOVE_TEAM_FROM_PROJECT_QUERY = 'REMOVE_TEAM_FROM_PROJECT_QUERY';
export const GET_CAN_CREATE_PROJECT_QUERY = 'GET_CAN_CREATE_PROJECT_QUERY';
export const ADD_EXTERNAL_TEAM_TO_PROJECT_QUERY =
  'ADD_EXTERNAL_TEAM_TO_PROJECT_QUERY';
export const ADD_MY_TEAM_TO_AVAILABLE_PROJECT_QUERY =
  'ADD_MY_TEAM_TO_AVAILABLE_PROJECT_QUERY';
export const ACCEPT_INVITE_QUERY = 'ACCEPT_INVITE_QUERY';
export const INVITE_TO_JOIN_PROJECT_QUERY = 'INVITE_TO_JOIN_PROJECT_QUERY';
export const INVITE_TO_JOIN_TEAM_QUERY = 'INVITE_TO_JOIN_TEAM_QUERY';
export const UPDATE_PROJECT_MEMBERS_QUERY = 'UPDATE_PROJECT_MEMBERS_QUERY';
export const UPDATE_TEAM_MEMBERS_QUERY = 'UPDATE_TEAM_MEMBERS_QUERY';
export const GET_UDEMY_COURSES_QUERY = 'GET_UDEMY_COURSES_QUERY';
export const GET_UDEMY_COURSE_QUERY = 'GET_UDEMY_COURSE_QUERY';
export const GET_STUDENT_WISHLIST_QUERY = 'GET_STUDENT_WISHLIST_QUERY';
export const ADD_TO_WISHLIST_QUERY = 'ADD_TO_WISHLIST_QUERY';
export const GET_MY_COURSES_QUERY = 'GET_MY_COURSES_QUERY';
export const ADD_TO_MY_COURSES_QUERY = 'ADD_TO_MY_COURSES_QUERY';
export const PROCESS_EXTERNAL_TEAM_INVITE_QUERY =
  'PROCESS_EXTERNAL_TEAM_INVITE_QUERY';
export const PROCESS_JOIN_REQUEST_QUERY = 'PROCESS_JOIN_REQUEST_QUERY';
export const GET_COURSE_ACTIVITIES_QUERY = 'GET_COURSE_ACTIVITIES_QUERY';
export const COURSE_AGREEMENT = 'COURSE_AGREEMENT';
export const VALIDATE_COURSE_PRIVILEGES_QUERY =
  'VALIDATE_COURSE_PRIVILEGES_QUERY';
export const GET_COURSERA_COURSES_QUERY = 'GET_COURSERA_COURSES_QUERY';
export const GET_UDACITY_COURSES_QUERY = 'GET_UDACITY_COURSES_QUERY';
export const GET_CATEGORIES_COUNT_QUERY = 'GET_CATEGORIES_COUNT_QUERY';
export const GET_GRANT_APPLICATIONS_QUERY = 'GET_GRANT_APPLICATIONS_QUERY';
export const GET_MENTORS_QUERY = 'GET_MENTORS_QUERY';
export const GET_MENTORS_BY_ID_QUERY = 'GET_MENTORS_BY_ID_QUERY';
export const GET_DASHBOARD_COUNT_QUERY = 'GET_DASHBOARD_COUNT_QUERY';
export const GET_CHAT_ROOMS_QUERY = 'GET_CHAT_ROOMS_QUERY';
export const GET_CHAT_ROOM_QUERY = 'GET_CHAT_ROOM_QUERY';
export const GET_CONVERSATION_ALL_CHANNELS_QUERY =
  'GET_CONVERSATION_ALL_CHANNELS_QUERY';
export const GET_CONVERSATION_MY_CHANNELS_QUERY =
  'GET_CONVERSATION_MY_CHANNELS_QUERY';
export const GET_CONVERSATION_MY_CREATED_CHANNELS_QUERY =
  'GET_CONVERSATION_MY_CREATED_CHANNELS_QUERY';
export const GET_CONVERSATION_CHANNEL_QUERY = 'GET_CONVERSATION_CHANNEL_QUERY';
export const GET_LINK_PREVIEW_QUERY = 'GET_LINK_PREVIEW_QUERY';
export const DELETE_CONVERSATION_CHANNEL_QUERY =
  'DELETE_CONVERSATION_CHANNEL_QUERY';
export const ADD_FEED_TOPIC_QUERY = 'ADD_FEED_TOPIC_QUERY';
export const UPDATE_FEED_TOPIC_QUERY = 'UPDATE_FEED_TOPIC_QUERY';
export const LIKE_FEED_TOPIC_QUERY = 'LIKE_FEED_TOPIC_QUERY';
export const LIKE_FEED_TOPIC_COMMENT_QUERY = 'LIKE_FEED_TOPIC_COMMENT_QUERY';
export const LIKE_FEED_TOPIC_COMMENT_REPLY_QUERY =
  'LIKE_FEED_TOPIC_COMMENT_REPLY_QUERY';
export const ADD_FEED_TOPIC_COMMENT_QUERY = 'ADD_FEED_TOPIC_COMMENT_QUERY';
export const DELETE_FEED_TOPIC_QUERY = 'DELETE_FEED_TOPIC_QUERY';
export const DELETE_FEED_TOPIC_COMMENT_QUERY =
  'DELETE_FEED_TOPIC_COMMENT_QUERY';
export const DELETE_FEED_TOPIC_COMMENT_REPLY_QUERY =
  'DELETE_FEED_TOPIC_COMMENT_REPLY_QUERY';
export const ADD_FEED_TOPIC_COMMENT_REPLY_QUERY =
  'ADD_FEED_TOPIC_COMMENT_REPLY_QUERY';
export const GET_FEED_TOPIC_COMMENTS_QUERY = 'GET_FEED_TOPIC_COMMENTS_QUERY';
export const GET_PUBLIC_FEED_TOPIC_COMMENTS_QUERY =
  'GET_PUBLIC_FEED_TOPIC_COMMENTS_QUERY';
export const GET_FEED_TOPIC_COMMENT_REPLIES_QUERY =
  'GET_FEED_TOPIC_COMMENT_REPLIES_QUERY';
export const GET_FEED_TOPICS_QUERY = 'GET_FEED_TOPICS_QUERY';
export const GET_PUBLIC_FEED_TOPICS_QUERY = 'GET_PUBLIC_FEED_TOPICS_QUERY';
export const GET_MEDIA_FEED_TOPICS_QUERY = 'GET_MEDIA_FEED_TOPICS_QUERY';
export const GET_FEED_TOPIC_QUERY = 'GET_FEED_TOPIC_QUERY';
export const GET_PUBLIC_FEED_TOPIC_QUERY = 'GET_PUBLIC_FEED_TOPIC_QUERY';
export const GET_FEED_MY_TOPICS_QUERY = 'GET_FEED_MY_TOPICS_QUERY';
export const GET_FEED_TOPICS_FOR_CHANNEL_QUERY =
  'GET_FEED_TOPICS_FOR_CHANNEL_QUERY';
export const UPDATE_CONVERSATION_CHANNEL_QUERY =
  'UPDATE_CONVERSATION_CHANNEL_QUERY';
export const GET_UNREAD_CONVERSATION_MESSAGES_QUERY =
  'GET_UNREAD_CONVERSATION_MESSAGES_QUERY';
export const GET_UNREAD_MESSAGES_QUERY = 'GET_UNREAD_MESSAGES_QUERY';
export const GET_COMMON_TEAM_MEMBERS_QUERY = 'GET_COMMON_TEAM_MEMBERS_QUERY';
export const GET_COMMON_TEAM_MEMBERS_INFINITE_QUERY =
  'GET_COMMON_TEAM_MEMBERS_INFINITE_QUERY';
export const DELETE_NOTIFICATION_QUERY = 'DELETE_NOTIFICATION_QUERY';
export const READ_NOTIFICATION_QUERY = 'READ_NOTIFICATION_QUERY';
export const GET_UNREAD_NOTIFICATION_COUNT_QUERY =
  'GET_UNREAD_NOTIFICATION_COUNT_QUERY';
export const GET_NOTIFICATIONS = 'GET_NOTIFICATIONS';
export const GET_UPICKS = 'GET_UPICKS';
export const GET_CAN_CREATE_TEAM_QUERY = 'GET_CAN_CREATE_TEAM_QUERY';
export const GET_CAN_ADD_MEMEBER_QUERY = 'GET_CAN_ADD_MEMEBER_QUERY';
export const GET_IACS = 'GET_IACS';
export const GET_GOAL_ADMINS = 'GET_GOAL_ADMINS';
export const ASSIGN_GOAL_ADMIN_QUERY = 'ASSIGN_GOAL_ADMIN_QUERY';
export const UNASSIGN_GOAL_ADMIN_QUERY = 'UNASSIGN_GOAL_ADMIN_QUERY';
export const GET_PROJECT_ADMINS = 'GET_PROJECT_ADMINS';
export const ASSIGN_PROJECT_ADMIN_QUERY = 'ASSIGN_PROJECT_ADMIN_QUERY';
export const UNASSIGN_PROJECT_ADMIN_QUERY = 'UNASSIGN_PROJECT_ADMIN_QUERY';
export const DELETE_PROJECT_QUERY = 'DELETE_PROJECT_QUERY';
export const ADD_UERS_FOR_SHARED_GOAL_QUERY = 'ADD_UERS_FOR_SHARED_GOAL_QUERY';
export const REMOVE_UERS_FROM_SHARED_GOAL_QUERY =
  'REMOVE_UERS_FROM_SHARED_GOAL_QUERY';
export const GET_USER_SHARED_GOALS_QUERY = 'GET_USER_SHARED_GOALS_QUERY';
export const GET_USER_SHARED_GOAL_USERS_QUERY =
  'GET_USER_SHARED_GOAL_USERS_QUERY';
export const UPDATE_PROJECT_QUERY = 'UPDATE_PROJECT_QUERY';
export const UPDATE_PROJECT_VISIBILITY_QUERY =
  'UPDATE_PROJECT_VISIBILITY_QUERY';
export const UPDATE_PROJECT_STATUS_QUERY = 'UPDATE_PROJECT_STATUS_QUERY';
export const UPDATE_MENTOR_QUERY = 'UPDATE_MENTOR_QUERY';
export const UPDATE_ONBOARD_QUERY = 'UPDATE_ONBOARD_QUERY';
export const UPDATE_CONVERSATION_MESSAGE_QUERY =
  'UPDATE_CONVERSATION_MESSAGE_QUERY';
export const UPDATE_MESSAGE_QUERY = 'UPDATE_MESSAGE_QUERY';
export const INVITE_OTHERS_QUERY = 'INVITE_OTHERS_QUERY';
export const SUBSCRIBE_TO_CHANNEL_QUERY = 'SUBSCRIBE_TO_CHANNEL_QUERY';
export const UNSUBSCRIBE_FROM_CHANNEL_QUERY = 'UNSUBSCRIBE_FROM_CHANNEL_QUERY';
export const JOIN_PUBLIC_CHANNEL_QUERY = 'JOIN_PUBLIC_CHANNEL_QUERY';
export const LEAVE_CHANNEL_QUERY = 'LEAVE_CHANNEL_QUERY';
export const REQUEST_TO_JOIN_CHANNEL_QUERY = 'REQUEST_TO_JOIN_CHANNEL_QUERY';
export const CANCEL_JOIN_REQUEST_QUERY = 'CANCEL_JOIN_REQUEST_QUERY';
export const ACCEPT_JOIN_REQUEST_QUERY = 'ACCEPT_JOIN_REQUEST_QUERY';
export const ACCEPT_INVITE_REQUEST_QUERY = 'ACCEPT_INVITE_REQUEST_QUERY';
export const REJECT_JOIN_REQUEST_QUERY = 'REJECT_JOIN_REQUEST_QUERY';
export const REJECT_INVITE_REQUEST_QUERY = 'REJECT_INVITE_REQUEST_QUERY';
export const ACCEPT_INVITE_OTHERS_QUERY = 'ACCEPT_INVITE_OTHERS_QUERY';
export const ADD_CONVERSATION_QUERY = 'ADD_CONVERSATION_QUERY';
export const GET_OWNER_GRANT_APPLICATION_QUERY =
  'GET_OWNER_GRANT_APPLICATION_QUERY';
export const GET_OWNER_GRANT_APPLICATIONS_QUERY =
  'GET_OWNER_GRANT_APPLICATIONS_QUERY';
export const GET_OWNER_GRANTS_QUERY = 'GET_OWNER_GRANTS_QUERY';
export const GET_OWNER_GRANT_QUERY = 'GET_OWNER_GRANT_QUERY';
export const GET_GRANTS_QUERY = 'GET_GRANTS_QUERY';
export const GET_GRANT_QUERY = 'GET_GRANT_QUERY';
export const ADD_GRANT_QUERY = 'ADD_GRANT_QUERY';
export const GET_OWNER_APPLICATION_STATS = 'GET_OWNER_APPLICATION_STATS';
export const DELETE_TEAM_QUERY = 'DELETE_TEAM_QUERY';
export const GET_BOOKS = 'GET_BOOKS';
export const GET_BOOK = 'GET_BOOK';
export const GET_BOOK_CHAPTERS = 'GET_BOOK_CHAPTERS';
export const GET_BOOK_CHAPTER = 'GET_BOOK_CHAPTER';
export const GET_CHAPTER_COMMENTS_QUERY = 'GET_CHAPTER_COMMENTS_QUERY';
export const ADD_CHAPTER_COMMENT_QUERY = 'ADD_CHAPTER_COMMENT_QUERY';
export const ADD_CHAPTER_NOTE_QUERY = 'ADD_CHAPTER_NOTE_QUERY';
export const UPDATE_CHAPTER_NOTE_QUERY = 'UPDATE_CHAPTER_NOTE_QUERY';
export const DELETE_CHAPTER_NOTE_QUERY = 'DELETE_CHAPTER_NOTE_QUERY';
export const GET_CHAPTER_NOTES_QUERY = 'GET_CHAPTER_NOTES_QUERY';
export const GET_CHAPTER_COMMENTS_INFINITE_QUERY =
  'GET_CHAPTER_COMMENTS_INFINITE_QUERY';
export const ADD_OBJECTIVE_QUERY = 'ADD_OBJECTIVE_QUERY';
export const UPDATE_OBJECTIVE_QUERY = 'UPDATE_OBJECTIVE_QUERY';
export const DELETE_OBJECTIVE_QUERY = 'DELETE_OBJECTIVE_QUERY';
export const GET_PROJECTS_OBJECTIVES_QUERY = 'GET_PROJECTS_OBJECTIVES_QUERY';
export const GET_OBJECTIVES_TASKS_QUERY = 'GET_OBJECTIVES_TASKS_QUERY';
export const GET_OBJECTIVE_QUERY = 'GET_OBJECTIVE_QUERY';
export const GET_OBJECTIVES_TASK_METRICS_QUERY =
  'GET_OBJECTIVES_TASK_METRICS_QUERY';
export const UNLINK_PROBLEM_STATEMENT_QUERY = 'UNLINK_PROBLEM_STATEMENT_QUERY';
export const DELETE_PROJECT_FILES_QUERY = 'DELETE_PROJECT_FILES_QUERY';
export const GET_TALENTS_INFINITE_QUERY = 'GET_TALENTS_INFINITE_QUERY';
export const GET_TALENTS_QUERY = 'GET_TALENTS_QUERY';
export const GET_TALENT_QUERY = 'GET_TALENT_QUERY';
export const USER_RESOURCES_BENEFICIARIES_FOR_SPONSOR_QUERY =
  'USER_RESOURCES_BENEFICIARIES_FOR_SPONSOR_QUERY';
export const CANCEL_SUBSCRIPTION_SPONSORSHIP_QUERY =
  'CANCEL_SUBSCRIPTION_SPONSORSHIP_QUERY';
export const CANCEL_MY_SUBSCRIPTION_SPONSORSHIP_QUERY =
  'CANCEL_MY_SUBSCRIPTION_SPONSORSHIP_QUERY';
export const USER_RESOURCES_BENEFICIARY_BY_USERID_QUERY =
  'USER_RESOURCES_BENEFICIARY_BY_USERID_QUERY';
export const USER_RESOURCES_BENEFICIARY_QUERY =
  'USER_RESOURCES_BENEFICIARY_QUERY';
export const USER_RESOURCES_QUERY = 'USER_RESOURCES_QUERY';
export const UPDATE_TALENT_QUERY = 'UPDATE_TALENT_QUERY';
export const GET_UNIVERSITY_STUDENTS = 'GET_UNIVERSITY_STUDENTS';
export const GET_UNIVERSITY_FACULTIES = 'GET_UNIVERSITY_FACULTIES';
export const GET_USER_PROJECTS = 'GET_USER_PROJECTS';
export const GET_USER_TEAMS = 'GET_USER_TEAMS';
export const MAKE_USER_ADMIN = 'MAKE_USER_ADMIN';
export const TOGGLE_ACCOUNT = 'TOGGLE_ACCOUNT';
export const REVOKE_ADMIN = 'REVOKE_ADMIN';
export const UPDATE_UNIVERSITY_USER = 'UPDATE_UNIVERSITY_USER';

// PivotL AI Agents Query Keys
export const GET_AI_AGENTS_QUERY = 'GET_AI_AGENTS_QUERY';
export const GET_AI_AGENT_SUITES_QUERY = 'GET_AI_AGENT_SUITES_QUERY';
export const GET_AI_AGENT_CATEGORIES_QUERY = 'GET_AI_AGENT_CATEGORIES_QUERY';
export const CLAIM_AGENT_SUITE_QUERY = 'CLAIM_AGENT_SUITE_QUERY';
