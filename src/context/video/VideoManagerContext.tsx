import {
  createContext,
  useContext,
  useRef,
  useCallback,
  ReactNode,
} from 'react';

interface VideoManagerContextType {
  registerVideo: (id: string, videoRef: HTMLVideoElement) => void;
  unregisterVideo: (id: string) => void;
  playVideo: (id: string) => void;
  pauseAllVideos: () => void;
  currentPlayingVideo: string | null;
}

const VideoManagerContext = createContext<VideoManagerContextType | null>(null);

export const useVideoManager = () => {
  const context = useContext(VideoManagerContext);
  if (!context) {
    // Provide fallback behavior when VideoManagerProvider is not available
    return {
      registerVideo: () => {},
      unregisterVideo: () => {},
      playVideo: () => {},
      pauseAllVideos: () => {},
      currentPlayingVideo: null,
    };
  }
  return context;
};

export const VideoManagerProvider = ({ children }: { children: ReactNode }) => {
  const videoRefs = useRef<Map<string, HTMLVideoElement>>(new Map());
  const currentPlayingRef = useRef<string | null>(null);

  const registerVideo = useCallback(
    (id: string, videoRef: HTMLVideoElement) => {
      videoRefs.current.set(id, videoRef);
    },
    [],
  );

  const unregisterVideo = useCallback((id: string) => {
    videoRefs.current.delete(id);
    if (currentPlayingRef.current === id) {
      currentPlayingRef.current = null;
    }
  }, []);

  const pauseAllVideos = useCallback(() => {
    videoRefs.current.forEach(video => {
      if (!video.paused) {
        video.pause();
      }
    });
    currentPlayingRef.current = null;
  }, []);

  const playVideo = useCallback(
    (id: string) => {
      // Pause all other videos first
      pauseAllVideos();

      const video = videoRefs.current.get(id);
      if (video) {
        video.play().catch(err => {
          console.warn('Failed to play video:', err);
        });
        currentPlayingRef.current = id;
      }
    },
    [pauseAllVideos],
  );

  const value: VideoManagerContextType = {
    registerVideo,
    unregisterVideo,
    playVideo,
    pauseAllVideos,
    currentPlayingVideo: currentPlayingRef.current,
  };

  return (
    <VideoManagerContext.Provider value={value}>
      {children}
    </VideoManagerContext.Provider>
  );
};
