import Keycloak from 'keycloak-js';
import { KEYCLOAK_REALM_URL } from '../utils/keycloakRealmUrl';

// Determine which realm to use based on current path
const getRealm = () => {
  if (typeof window !== 'undefined') {
    return window.location.pathname.startsWith('/pivotl')
      ? 'agentic-identity-service'
      : 'upivotal-identity-service';
  }
  // Default realm for initial render
  return 'upivotal-identity-service';
};

const getClientId = () => {
  if (typeof window !== 'undefined') {
    return window.location.pathname.startsWith('/pivotl')
      ? 'agentic-frontend-app'
      : 'upivotal-frontend-app';
  }
  // Default clientId for initial render
  return 'upivotal-frontend-app';
};

const keycloak = new Keycloak({
  url: KEYCLOAK_REALM_URL,
  realm: getRealm(),
  clientId: getClientId(),
});

export default keycloak;
